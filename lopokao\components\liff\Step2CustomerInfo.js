/**
 * LIFF 訂購系統 - 步驟2：客戶資訊
 */

function Step2CustomerInfo({ orderData, setOrderData, userProfile, onNext, onPrev }) {
    const [districts, setDistricts] = useState([]);
    const [showStorePicker, setShowStorePicker] = useState(false);
    const [addressWarning, setAddressWarning] = useState('');
    const [availableDates, setAvailableDates] = useState([]);
    const [datesLoading, setDatesLoading] = useState(false);
    
    // 初始化檢查：確保 cityDistricts 已載入
    useEffect(() => {
        const checkCityDistricts = () => {
            if (window.cityDistricts) {
                console.log('✅ cityDistricts 已成功載入，包含', Object.keys(window.cityDistricts).length, '個縣市');
            } else {
                console.warn('⚠️ cityDistricts 未載入，使用後備資料...');
                // 提供後備的基本縣市資料
                window.cityDistricts = {
                    "臺北市": ["中正區", "大同區", "中山區", "松山區", "大安區", "萬華區", "信義區", "士林區", "北投區", "內湖區", "南港區", "文山區"],
                    "新北市": ["板橋區", "三重區", "中和區", "永和區", "新莊區", "新店區", "樹林區", "鶯歌區", "三峽區", "淡水區", "汐止區", "瑞芳區", "土城區", "蘆洲區", "五股區", "泰山區", "林口區"],
                    "桃園市": ["桃園區", "中壢區", "平鎮區", "八德區", "楊梅區", "蘆竹區", "大溪區", "龜山區", "大園區", "觀音區", "新屋區", "龍潭區", "復興區"],
                    "臺中市": ["中區", "東區", "南區", "西區", "北區", "北屯區", "西屯區", "南屯區", "太平區", "大里區", "霧峰區", "烏日區", "豐原區", "后里區", "石岡區", "東勢區", "和平區", "新社區", "潭子區", "大雅區", "神岡區", "大肚區", "沙鹿區", "龍井區", "梧棲區", "清水區", "大甲區", "外埔區", "大安區"],
                    "臺南市": ["中西區", "東區", "南區", "北區", "安平區", "安南區", "永康區", "歸仁區", "新化區", "左鎮區", "玉井區", "楠西區", "南化區", "仁德區", "關廟區", "龍崎區", "官田區", "麻豆區", "佳里區", "西港區", "七股區", "將軍區", "學甲區", "北門區", "新營區", "後壁區", "白河區", "東山區", "六甲區", "下營區", "柳營區", "鹽水區", "善化區", "大內區", "山上區", "新市區", "安定區"],
                    "高雄市": ["楠梓區", "左營區", "鼓山區", "三民區", "鹽埕區", "前金區", "新興區", "苓雅區", "前鎮區", "旗津區", "小港區", "鳳山區", "大寮區", "鳥松區", "林園區", "仁武區", "大樹區", "大社區", "岡山區", "路竹區", "橋頭區", "梓官區", "彌陀區", "永安區", "燕巢區", "田寮區", "阿蓮區", "茄萣區", "湖內區", "旗山區", "美濃區", "內門區", "杉林區", "甲仙區", "六龜區", "茂林區", "桃源區", "那瑪夏區"]
                };
                console.log('✅ 後備 cityDistricts 已載入');
            }
        };
        checkCityDistricts();
    }, []);
    
    // 載入地區資料
    useEffect(() => {
        // 除錯：檢查 cityDistricts 是否正確載入
        if (!window.cityDistricts) {
            console.error('cityDistricts 未載入，請檢查 utils/cityDistricts.js');
            return;
        }
        
        if (orderData.district) {
            const areas = window.cityDistricts[orderData.district] || [];
            console.log(`載入 ${orderData.district} 的地區:`, areas);
            setDistricts(areas);
        } else {
            setDistricts([]);
        }
    }, [orderData.district]);
    
    // 獲取可選日期
    const fetchAvailableDates = async (deliveryMethod) => {
        try {
            setDatesLoading(true);
            const response = await fetch(`./api/delivery_settings.php?action=available_dates&delivery_method=${encodeURIComponent(deliveryMethod)}`);
            const result = await response.json();
            
            if (result.success) {
                setAvailableDates(result.data);
            } else {
                console.error('獲取可選日期失敗:', result.message);
                setAvailableDates(getDefaultDates());
            }
        } catch (error) {
            console.error('獲取可選日期錯誤:', error);
            setAvailableDates(getDefaultDates());
        } finally {
            setDatesLoading(false);
        }
    };
    
    // 預設日期（如果 API 失敗時使用）
    const getDefaultDates = () => {
        const dates = [];
        const today = new Date();
        
        for (let i = 1; i <= 7; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() + i);
            const dateStr = date.toISOString().split('T')[0];
            const displayStr = `${date.getMonth() + 1}/${date.getDate()} (${['日', '一', '二', '三', '四', '五', '六'][date.getDay()]})`;
            
            dates.push({
                value: dateStr,
                display: displayStr
            });
        }
        
        return dates;
    };
    
    // 配送方式變更處理
    const handleDeliveryMethodChange = (method) => {
        setOrderData(prev => ({
            ...prev,
            deliveryMethod: method,
            storeName: '',
            storeAddress: '',
            storeId: '',
            preferredDate: '',
            district: '',
            area: '',
            address: ''
        }));
        
        setAddressWarning('');
        
        if (method) {
            fetchAvailableDates(method);
        }
    };
    
    // 地址驗證
    const handleAddressChange = (address) => {
        setOrderData(prev => ({ ...prev, address }));
        
        if (address.trim() && orderData.deliveryMethod === '宅配到府') {
            const hasNumber = /\d+號/.test(address);
            if (!hasNumber && address.length > 3) {
                setAddressWarning('⚠️ 地址須包含門牌號碼或數字後方有(號)字');
            } else {
                setAddressWarning('');
            }
        } else {
            setAddressWarning('');
        }
    };
    
    // 門市選擇
    const openStorePicker = () => {
        setShowStorePicker(true);
    };
    
    const closeStorePicker = () => {
        setShowStorePicker(false);
    };
    
    const setStoreInfo = (name, address, id) => {
        setOrderData(prev => ({
            ...prev,
            storeName: name,
            storeAddress: address,
            storeId: id
        }));
        setShowStorePicker(false);
    };
    
    // 設定全域函數供 iframe 使用
    useEffect(() => {
        window.setStoreInfo = setStoreInfo;
        window.closeStorePicker = closeStorePicker;
    }, []);
    
    // 表單驗證
    const validateForm = () => {
        const requiredFields = [
            { field: 'customerName', name: '姓名' },
            { field: 'phone', name: '電話' },
            { field: 'deliveryMethod', name: '配送方式' },
            { field: 'preferredDate', name: '希望到貨日期' },
            { field: 'contactMethod', name: '聯繫方式' },
            { field: 'paymentMethod', name: '付款方式' }
        ];
        
        for (const { field, name } of requiredFields) {
            if (!orderData[field] || orderData[field].trim() === '') {
                window.showError(`請填寫*${name}*`);
                return false;
            }
        }
        
        // 配送方式相關驗證
        if (orderData.deliveryMethod === '宅配到府') {
            if (!orderData.district || !orderData.area || !orderData.address) {
                window.showError('宅配到府: *請填寫完整地址資訊*');
                return false;
            }
            
            const hasNumber = /\d+號/.test(orderData.address.trim());
            if (!hasNumber) {
                window.showError('地址須含門牌號碼或數字後方須有(號)字');
                return false;
            }
        } else if (orderData.deliveryMethod === '7-11門市') {
            if (!orderData.storeName || !orderData.storeAddress) {
                window.showError('請選擇7-11取貨門市');
                return false;
            }
        }
        
        // 電話號碼格式驗證
        const cleanPhone = orderData.phone.replace(/\D/g, '');
        const phoneRegex = /^(09\d{8}|(0[2-8])\d{7,})$/;
        if (!phoneRegex.test(cleanPhone)) {
            window.showError('電話號碼格式不正確\n\n手機請輸入10碼 (如 0912345678)\n市話請輸入區碼+號碼 (如 0229998888)');
            return false;
        }
        
        return true;
    };
    
    const handleNext = () => {
        if (validateForm()) {
            onNext();
        }
    };
    
    return (
        <div className="space-y-4">
            {/* 標題 */}
            <div className="text-center mb-6">
                <h2 className="text-2xl font-bold text-gray-800 mb-2">填寫資料</h2>
                <p className="text-gray-600">請填寫客戶資料和配送資訊</p>
            </div>
            
            <div className="scroll-area space-y-4">
                {/* 客戶基本資料 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">👤</span>
                        客戶資料
                        {userProfile && (
                            <span className="ml-2 text-xs bg-green-100 text-green-700 px-2 py-1 rounded">
                                LINE 預填
                            </span>
                        )}
                    </h3>
                    
                    <div className="space-y-3">
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">姓名 *</label>
                            <input
                                type="text"
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                value={orderData.customerName}
                                onChange={(e) => setOrderData(prev => ({ ...prev, customerName: e.target.value }))}
                                placeholder="請輸入您的姓名"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">電話 *</label>
                            <input
                                type="tel"
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                value={orderData.phone}
                                onChange={(e) => setOrderData(prev => ({ ...prev, phone: e.target.value }))}
                                placeholder="0912345678 或 0229998888"
                            />
                        </div>
                    </div>
                </div>
                
                {/* 配送方式 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">🚚</span>
                        配送方式
                    </h3>
                    
                    <div className="space-y-2">
                        <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input
                                type="radio"
                                name="deliveryMethod"
                                value="宅配到府"
                                checked={orderData.deliveryMethod === '宅配到府'}
                                onChange={(e) => handleDeliveryMethodChange(e.target.value)}
                                className="mr-3"
                            />
                            <div>
                                <div className="font-medium">🏠 宅配到府</div>
                                <div className="text-sm text-gray-600">配送到指定地址</div>
                            </div>
                        </label>
                        
                        <label className="flex items-center p-3 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50">
                            <input
                                type="radio"
                                name="deliveryMethod"
                                value="7-11門市"
                                checked={orderData.deliveryMethod === '7-11門市'}
                                onChange={(e) => handleDeliveryMethodChange(e.target.value)}
                                className="mr-3"
                            />
                            <div>
                                <div className="font-medium">🏪 超商取貨 (7-11)</div>
                                <div className="text-sm text-gray-600">到指定門市取貨</div>
                            </div>
                        </label>
                    </div>
                </div>
                
                {/* 地址資訊 - 宅配到府 */}
                {orderData.deliveryMethod === '宅配到府' && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                            <span className="mr-2">📍</span>
                            配送地址
                        </h3>
                        
                        <div className="space-y-3">
                            <div className="grid grid-cols-2 gap-3">
                                <div>
                                    <label className="block text-gray-700 mb-1 text-sm font-medium">縣市 *</label>
                                    <select
                                        className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                        value={orderData.district}
                                        onChange={(e) => setOrderData(prev => ({ ...prev, district: e.target.value, area: '' }))}
                                    >
                                        <option value="">
                                            {window.cityDistricts ? '請選擇縣市' : '載入中...'}
                                        </option>
                                        {window.cityDistricts ? Object.keys(window.cityDistricts).map(city => (
                                            <option key={city} value={city}>{city}</option>
                                        )) : (
                                            <option value="" disabled>正在載入縣市資料...</option>
                                        )}
                                    </select>
                                </div>
                                
                                <div>
                                    <label className="block text-gray-700 mb-1 text-sm font-medium">地區 *</label>
                                    <select
                                        className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                        value={orderData.area}
                                        onChange={(e) => setOrderData(prev => ({ ...prev, area: e.target.value }))}
                                        disabled={!orderData.district}
                                    >
                                        <option value="">請選擇地區</option>
                                        {districts.map(area => (
                                            <option key={area} value={area}>{area}</option>
                                        ))}
                                    </select>
                                </div>
                            </div>
                            
                            <div>
                                <label className="block text-gray-700 mb-1 text-sm font-medium">詳細地址 *</label>
                                <input
                                    type="text"
                                    className={`w-full px-3 py-3 border rounded-lg text-base ${
                                        addressWarning ? 'border-orange-400' : 'border-gray-300'
                                    }`}
                                    value={orderData.address}
                                    onChange={(e) => handleAddressChange(e.target.value)}
                                    placeholder="請輸入完整地址（例如：中正路123號）"
                                />
                                {addressWarning && (
                                    <p className="text-orange-600 text-sm mt-1">{addressWarning}</p>
                                )}
                            </div>
                        </div>
                    </div>
                )}
                
                {/* 門市選擇 - 超商取貨 */}
                {orderData.deliveryMethod === '7-11門市' && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <div className="flex justify-between items-center mb-3">
                            <h3 className="font-bold text-gray-800 flex items-center">
                                <span className="mr-2">🏪</span>
                                取貨門市
                            </h3>
                            <button
                                type="button"
                                onClick={openStorePicker}
                                className="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium"
                            >
                                選擇門市
                            </button>
                        </div>
                        
                        <div className="border rounded-lg p-3 bg-gray-50">
                            {orderData.storeName ? (
                                <div>
                                    <p className="font-semibold text-green-600 mb-1">✓ 已選擇門市</p>
                                    <p className="font-medium">{orderData.storeName} 門市</p>
                                    <p className="text-gray-600 text-sm">{orderData.storeAddress}</p>
                                    <p className="text-gray-600 text-sm">店號：{orderData.storeId}</p>
                                </div>
                            ) : (
                                <p className="text-gray-500">尚未選擇門市</p>
                            )}
                        </div>
                    </div>
                )}
                
                {/* 配送時間 */}
                {orderData.deliveryMethod && (
                    <div className="bg-white p-4 rounded-lg border border-gray-200">
                        <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                            <span className="mr-2">📅</span>
                            配送時間
                        </h3>
                        
                        <div className="space-y-3">
                            <div>
                                <label className="block text-gray-700 mb-1 text-sm font-medium">希望到貨日期 *</label>
                                <select
                                    className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                    value={orderData.preferredDate}
                                    onChange={(e) => setOrderData(prev => ({ ...prev, preferredDate: e.target.value }))}
                                    disabled={datesLoading}
                                >
                                    <option value="">
                                        {datesLoading ? '載入中...' : '請選擇到貨日期'}
                                    </option>
                                    {availableDates.map(date => (
                                        <option key={date.value} value={date.value}>{date.display}</option>
                                    ))}
                                </select>
                            </div>
                            
                            <div>
                                <label className="block text-gray-700 mb-1 text-sm font-medium">希望到貨時間 *</label>
                                <select
                                    className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                    value={orderData.preferredTime}
                                    onChange={(e) => setOrderData(prev => ({ ...prev, preferredTime: e.target.value }))}
                                >
                                    <option value="上午 (13點前)">上午 (13點前)</option>
                                    <option value="下午 (13-18點)">下午 (13-18點)</option>
                                </select>
                            </div>
                        </div>
                    </div>
                )}
                
                {/* 聯繫方式與付款 */}
                <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <h3 className="font-bold text-gray-800 mb-3 flex items-center">
                        <span className="mr-2">💬</span>
                        聯繫與付款
                    </h3>
                    
                    <div className="space-y-3">
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">聯繫方式 *</label>
                            <select
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                value={orderData.contactMethod}
                                onChange={(e) => setOrderData(prev => ({ ...prev, contactMethod: e.target.value }))}
                            >
                                <option value="">請選擇聯繫方式</option>
                                <option value="line">Line</option>
                                <option value="facebook">Facebook</option>
                                <option value="phone">電話</option>
                            </select>
                        </div>
                        
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">社群帳號名稱</label>
                            <input
                                type="text"
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                value={orderData.socialAccount}
                                onChange={(e) => setOrderData(prev => ({ ...prev, socialAccount: e.target.value }))}
                                placeholder="請輸入您的社群帳號名稱"
                            />
                        </div>
                        
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">付款方式 *</label>
                            <select
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                value={orderData.paymentMethod}
                                onChange={(e) => setOrderData(prev => ({ ...prev, paymentMethod: e.target.value }))}
                            >
                                <option value="">請選擇付款方式</option>
                                <option value="銀行轉帳">銀行轉帳</option>
                                <option value="貨到付款">貨到付款</option>
                            </select>
                        </div>
                        
                        {orderData.paymentMethod === '銀行轉帳' && (
                            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                                <h4 className="font-bold text-blue-800 mb-2">銀行帳戶資訊</h4>
                                <div className="text-sm text-blue-700 space-y-1">
                                    <p>銀行：中國信託 (代碼：822)</p>
                                    <p>帳號：222540600078</p>
                                </div>
                            </div>
                        )}
                        
                        <div>
                            <label className="block text-gray-700 mb-1 text-sm font-medium">備註</label>
                            <textarea
                                className="w-full px-3 py-3 border border-gray-300 rounded-lg text-base"
                                rows="3"
                                value={orderData.notes}
                                onChange={(e) => setOrderData(prev => ({ ...prev, notes: e.target.value }))}
                                placeholder="有任何特殊需求請在此註明"
                            />
                        </div>
                    </div>
                </div>
            </div>
            
            {/* 導航按鈕 */}
            <div className="flex space-x-3 pt-4">
                <button
                    onClick={onPrev}
                    className="primary-btn flex-1 bg-gray-300 hover:bg-gray-400 text-gray-700"
                >
                    ← 上一步
                </button>
                <button
                    onClick={handleNext}
                    className="primary-btn flex-1 bg-red-500 hover:bg-red-600 text-white"
                >
                    下一步：確認訂單 →
                </button>
            </div>
            
            {/* 門市選擇器彈窗 */}
            {showStorePicker && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ margin: '-20px' }}>
                    <div className="bg-white rounded-lg w-full max-w-md mx-4 h-5/6 flex flex-col">
                        <div className="flex justify-between items-center p-4 border-b">
                            <h3 className="text-lg font-bold">選擇7-11門市</h3>
                            <button
                                onClick={closeStorePicker}
                                className="text-gray-500 hover:text-gray-700 text-2xl"
                            >
                                ×
                            </button>
                        </div>
                        <div className="flex-1 overflow-hidden">
                            <iframe
                                src="store-selector.php"
                                className="w-full h-full border-0"
                                title="門市選擇器"
                            />
                        </div>
                    </div>
                </div>
            )}
        </div>
    );
}

// 全域導出
window.Step2CustomerInfo = Step2CustomerInfo;