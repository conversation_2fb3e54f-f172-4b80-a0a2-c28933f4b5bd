import React from 'react';

// 精美的 SVG 圖示組件
const CheckCircleIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const XCircleIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const ExclamationTriangleIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
    </svg>
);

const InformationCircleIcon = () => (
    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
);

const CloseIcon = () => (
    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
    </svg>
);


// 獲取圖標和顏色主題
const getAlertTheme = (type) => {
    switch (type) {
        case 'success':
            return {
                Icon: CheckCircleIcon,
                bgColor: 'bg-green-50',
                borderColor: 'border-green-200',
                iconBg: 'bg-green-100',
                iconColor: 'text-green-600',
                titleColor: 'text-green-900',
                buttonColor: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
            };
        case 'error':
            return {
                Icon: XCircleIcon,
                bgColor: 'bg-red-50',
                borderColor: 'border-red-200',
                iconBg: 'bg-red-100',
                iconColor: 'text-red-600',
                titleColor: 'text-red-900',
                buttonColor: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
            };
        case 'warning':
            return {
                Icon: ExclamationTriangleIcon,
                bgColor: 'bg-yellow-50',
                borderColor: 'border-yellow-200',
                iconBg: 'bg-yellow-100',
                iconColor: 'text-yellow-600',
                titleColor: 'text-yellow-900',
                buttonColor: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
            };
        default:
            return {
                Icon: InformationCircleIcon,
                bgColor: 'bg-blue-50',
                borderColor: 'border-blue-200',
                iconBg: 'bg-blue-100',
                iconColor: 'text-blue-600',
                titleColor: 'text-blue-900',
                buttonColor: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
            };
    }
};

function CustomAlert({ isVisible, title, message, type = 'info', onClose }) {
    const theme = getAlertTheme(type);

    if (!isVisible) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            {/* 背景遮罩 - 添加動畫效果 */}
            <div
                className={`absolute inset-0 bg-black transition-opacity duration-300 ${
                    isVisible ? 'opacity-60' : 'opacity-0'
                }`}
                onClick={onClose}
            />

            {/* 彈窗主體 - 添加動畫效果 */}
            <div className={`relative z-10 w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden transform transition-all duration-300 ${
                isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'
            }`}>
                {/* 標題和圖示 */}
                <div className={`flex items-center p-5 ${theme.bgColor} border-b ${theme.borderColor}`}>
                    <div className={`flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full ${theme.iconBg} ${theme.iconColor}`}>
                        <theme.Icon />
                    </div>
                    <div className="ml-4 flex-1">
                        <h3 className={`text-lg font-semibold ${theme.titleColor}`}>
                            {title}
                        </h3>
                    </div>
                    <button
                        onClick={onClose}
                        className="flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100"
                        aria-label="關閉"
                    >
                        <CloseIcon />
                    </button>
                </div>

                {/* 內容 */}
                <div className="p-6">
                    <p className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap break-words">
                        {message}
                    </p>
                </div>

                {/* 按鈕 */}
                <div className="px-6 py-4 bg-gray-50 flex justify-end">
                    <button
                        onClick={onClose}
                        className={`px-8 py-2.5 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${theme.buttonColor}`}
                    >
                        確定
                    </button>
                </div>
            </div>
        </div>
    );
}

export default CustomAlert;