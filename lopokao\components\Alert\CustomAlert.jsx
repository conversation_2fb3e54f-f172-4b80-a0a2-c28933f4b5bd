import React from 'react';
import { Transition } from '@headlessui/react'; // 建議安裝 @headlessui/react 來做動畫

// 建議使用 heroicons 或類似的 SVG 圖示庫
// 這裡用 JSX 簡單模擬 SVG 圖示
const CheckCircleIcon = () => <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
const XCircleIcon = () => <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;
const ExclamationTriangleIcon = () => <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" /></svg>;
const InformationCircleIcon = () => <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>;


// 獲取圖標和顏色主題
const getAlertTheme = (type) => {
    switch (type) {
        case 'success': return { Icon: CheckCircleIcon, color: 'green' };
        case 'error': return { Icon: XCircleIcon, color: 'red' };
        case 'warning': return { Icon: ExclamationTriangleIcon, color: 'yellow' };
        default: return { Icon: InformationCircleIcon, color: 'blue' };
    }
};

function CustomAlert({ isVisible, title, message, type, onClose }) {
  const theme = getAlertTheme(type);
  const color = theme.color;

  return (
    <Transition show={isVisible} as={React.Fragment}>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* 背景遮罩 */}
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0"
          enterTo="opacity-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100"
          leaveTo="opacity-0"
        >
          <div className="absolute inset-0 bg-black/60" onClick={onClose} />
        </Transition.Child>

        {/* 彈窗主體 */}
        <Transition.Child
          as={React.Fragment}
          enter="ease-out duration-300"
          enterFrom="opacity-0 scale-95"
          enterTo="opacity-100 scale-100"
          leave="ease-in duration-200"
          leaveFrom="opacity-100 scale-100"
          leaveTo="opacity-0 scale-95"
        >
          <div className={`relative z-10 w-full max-w-md mx-4 bg-white rounded-2xl shadow-xl overflow-hidden`}>
            {/* 標題和圖示 */}
            <div className={`flex items-center p-5 bg-${color}-50 border-b border-${color}-100`}>
              <div className={`flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full bg-${color}-100 text-${color}-600`}>
                <theme.Icon />
              </div>
              <div className="ml-4">
                <h3 className={`text-lg font-semibold text-${color}-900`}>
                  {title}
                </h3>
              </div>
               <button onClick={onClose} className="absolute top-3 right-3 text-gray-400 hover:text-gray-600">
                   <XCircleIcon />
               </button>
            </div>
            
            {/* 內容 */}
            <div className="p-6">
              <p className="text-base text-gray-700 leading-relaxed whitespace-pre-wrap break-words">
                {message}
              </p>
            </div>

            {/* 按鈕 */}
            <div className="px-6 py-4 bg-gray-50 flex justify-end">
              <button
                onClick={onClose}
                className={`px-8 py-2.5 bg-${color}-600 text-white font-semibold rounded-lg shadow-md hover:bg-${color}-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-${color}-500 transition-all duration-200`}
              >
                確定
              </button>
            </div>
          </div>
        </Transition.Child>
      </div>
    </Transition>
  );
}

export default CustomAlert;