/**
 * LIFF 訂購系統 - 進度指示器組件
 */

function ProgressIndicator({ currentStep }) {
    const steps = [
        { step: 1, title: '選擇商品', icon: '🛒' },
        { step: 2, title: '填寫資料', icon: '📝' },
        { step: 3, title: '確認訂單', icon: '✅' }
    ];
    
    return (
        <div className="bg-white shadow-sm border-b">
            <div className="px-4 py-3">
                <div className="flex items-center justify-between">
                    {steps.map((stepInfo, index) => (
                        <React.Fragment key={stepInfo.step}>
                            <div className="flex flex-col items-center">
                                <div 
                                    className={`progress-step w-10 h-10 rounded-full flex items-center justify-center text-sm font-medium ${
                                        stepInfo.step < currentStep 
                                            ? 'completed' 
                                            : stepInfo.step === currentStep 
                                                ? 'active bg-red-500 text-white' 
                                                : 'bg-gray-200 text-gray-500'
                                    }`}
                                >
                                    {stepInfo.step < currentStep ? '✓' : stepInfo.step}
                                </div>
                                <span className={`text-xs mt-1 ${
                                    stepInfo.step === currentStep 
                                        ? 'text-red-500 font-medium' 
                                        : 'text-gray-500'
                                }`}>
                                    {stepInfo.title}
                                </span>
                            </div>
                            
                            {/* 連接線 */}
                            {index < steps.length - 1 && (
                                <div 
                                    className={`flex-1 h-0.5 mx-2 ${
                                        stepInfo.step < currentStep 
                                            ? 'bg-green-500' 
                                            : 'bg-gray-200'
                                    }`}
                                    style={{ marginTop: '-20px' }}
                                />
                            )}
                        </React.Fragment>
                    ))}
                </div>
                
                {/* 步驟說明 */}
                <div className="mt-3 text center">
                    <p className="text-sm text-gray-600">
                        {currentStep === 1 }
                        {currentStep === 2 }
                        {currentStep === 3 }
                    </p>
                </div>
            </div>
        </div>
    );
}

// 全域導出
window.ProgressIndicator = ProgressIndicator;