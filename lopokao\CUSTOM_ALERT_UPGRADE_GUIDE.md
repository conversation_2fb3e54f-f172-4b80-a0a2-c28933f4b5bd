# CustomAlert 升級指南

## 🎯 升級概述

已成功將專案中的 CustomAlert 系統從舊版升級到新版，採用現代化的 React Context 架構，同時保持向後兼容性。

## 📁 檔案結構

```
lopokao/
├── components/
│   ├── Alert/
│   │   └── CustomAlert.jsx          # 新版 UI 組件（主要）
│   └── liff/
│       ├── CustomAlert.js           # 舊版組件（保留）
│       └── CustomAlertNew.js        # LIFF 專用新版組件
├── context/
│   └── AlertContext.jsx             # Context Provider
└── test-new-alert.html              # 測試頁面
```

## 🔄 升級內容

### 1. **新版 CustomAlert.jsx**
- ✅ 使用精美的 SVG 圖示
- ✅ 流暢的動畫效果
- ✅ 更好的視覺設計
- ✅ 響應式佈局
- ✅ 無障礙功能支援

### 2. **AlertContext.jsx**
- ✅ React Context API 架構
- ✅ 向後兼容的全域函數
- ✅ useAlert Hook 支援
- ✅ 統一的狀態管理

### 3. **向後兼容性**
- ✅ 保持原有的 `window.showSuccess()` 等函數
- ✅ LIFF 頁面使用專用版本
- ✅ 現有程式碼無需修改

## 🚀 使用方式

### 方式一：全域函數（向後兼容）
```javascript
// 成功訊息
window.showSuccess('訂單提交成功！', '融氏古早味');

// 錯誤訊息
window.showError('訂單提交失敗！', '系統錯誤');

// 警告訊息
window.showWarning('請注意配送時間！', '配送提醒');

// 資訊訊息
window.showInfo('歡迎使用系統！', '系統資訊');
```

### 方式二：React Hook（推薦）
```javascript
import { useAlert } from './context/AlertContext';

function MyComponent() {
    const { showSuccess, showError, showWarning, showInfo } = useAlert();
    
    const handleSubmit = () => {
        showSuccess('操作成功！');
    };
    
    return <button onClick={handleSubmit}>提交</button>;
}
```

### 方式三：Context 直接使用
```javascript
import { useAlert } from './context/AlertContext';

function MyComponent() {
    const { showAlert } = useAlert();
    
    const handleCustomAlert = () => {
        showAlert('自定義訊息', {
            title: '自定義標題',
            type: 'warning',
            onConfirm: () => console.log('確認後執行')
        });
    };
    
    return <button onClick={handleCustomAlert}>自定義彈窗</button>;
}
```

## 🎨 視覺改進

### 舊版 vs 新版對比

| 特性 | 舊版 | 新版 |
|------|------|------|
| 圖示 | Emoji (✅❌⚠️ℹ️) | 精美 SVG 圖示 |
| 動畫 | 基礎 transition | 流暢的進場/退場動畫 |
| 設計 | 基礎樣式 | 現代化設計，圓角、陰影 |
| 關閉方式 | 僅確定按鈕 | 確定按鈕 + 右上角關閉 + 背景點擊 |
| 響應式 | 基本支援 | 完全響應式 |

## 🔧 技術改進

### 架構優化
- **舊版**：直接操作 `window` 物件
- **新版**：React Context + Hook 架構

### 程式碼品質
- **舊版**：單一檔案，混合邏輯
- **新版**：分離關注點，可測試性更好

### 維護性
- **舊版**：難以擴展和維護
- **新版**：模組化，易於擴展

## 📱 LIFF 頁面支援

LIFF 頁面使用專用的 `CustomAlertNew.js`，提供：
- 與新版相同的視覺效果
- 向後兼容的 API
- 適合 script 標籤載入的格式

## 🧪 測試

### 測試頁面
開啟 `test-new-alert.html` 來測試所有功能：
```bash
# 在瀏覽器中開啟
http://localhost/lopokao/test-new-alert.html
```

### 測試項目
- ✅ 成功訊息顯示
- ✅ 錯誤訊息顯示
- ✅ 警告訊息顯示
- ✅ 資訊訊息顯示
- ✅ 長訊息處理
- ✅ 動畫效果
- ✅ 響應式佈局

## 🔄 遷移建議

### 立即可用
- 現有程式碼無需修改
- 全域函數繼續正常工作
- 視覺效果自動升級

### 未來優化
建議逐步將程式碼遷移到 Hook 方式：

```javascript
// 舊方式（仍可用）
window.showSuccess('成功！');

// 新方式（推薦）
const { showSuccess } = useAlert();
showSuccess('成功！');
```

## 🎯 總結

✅ **完成項目**
- 新版 CustomAlert UI 組件
- React Context 架構
- 向後兼容性保持
- LIFF 頁面支援
- 測試頁面建立

✅ **優勢**
- 更美觀的視覺效果
- 更好的使用者體驗
- 更現代的程式碼架構
- 更容易維護和擴展
- 完全向後兼容

現在您可以享受新版 CustomAlert 帶來的所有改進，同時現有功能完全不受影響！

## 🔄 漸進式升級完整方案

### 階段一：向後兼容（已完成）
✅ 保持所有現有的 `window.showSuccess()` 等函數
✅ 自動升級視覺效果
✅ 零程式碼修改需求

### 階段二：安全包裝器（推薦）
使用 `SafeAlert` 包裝器，提供更安全的調用方式：

```javascript
// 替代 window.showSuccess()
SafeAlert.success('訂單成功！', '融氏古早味');

// 替代 window.showError()
SafeAlert.error('訂單失敗！', '系統錯誤');

// 自動降級支援
SafeAlert.warning('警告訊息'); // 如果新版不可用，自動使用舊版
```

### 階段三：React Hook（最佳實踐）
在 React 組件中使用 Hook：

```javascript
import { useAlert } from './context/AlertContext';

function OrderComponent() {
    const { showSuccess, showError } = useAlert();

    const handleSubmit = async () => {
        try {
            await submitOrder();
            showSuccess('訂單提交成功！');
        } catch (error) {
            showError('訂單提交失敗：' + error.message);
        }
    };
}
```

## 🧪 測試和驗證

### 測試頁面
1. **基本功能測試**：`test-new-alert.html`
2. **漸進式升級測試**：`test-progressive-upgrade.html`
3. **主應用測試**：`index.html`

### 升級狀態檢查
在瀏覽器控制台中執行：
```javascript
// 顯示升級狀態報告
AlertUpgradeUtils.showUpgradeReport();

// 檢查是否已升級
console.log('已升級:', AlertUpgradeUtils.isAlertSystemUpgraded());

// 檢查版本
console.log('版本:', AlertUpgradeUtils.getAlertSystemVersion());
```

## 📁 新增檔案清單

```
lopokao/
├── utils/
│   └── alertUpgradeUtils.js          # 升級工具和安全包裝器
├── components/
│   └── examples/
│       └── AlertMigrationExample.js  # 升級示範組件
├── test-progressive-upgrade.html     # 漸進式升級測試頁面
└── CUSTOM_ALERT_UPGRADE_GUIDE.md    # 本指南文件
```

## 🎯 升級建議時程

### 立即可用（第一週）
- ✅ 現有程式碼自動享受新視覺效果
- ✅ 所有功能正常運作
- ✅ 零風險升級

### 短期優化（第二週）
- 🔄 將關鍵功能改用 `SafeAlert`
- 🔄 在新開發的組件中使用 Hook
- 🔄 測試混合使用場景

### 長期重構（第三週以後）
- 🔄 逐步將舊版調用改為 Hook
- 🔄 移除對全域函數的依賴
- 🔄 完全採用 Context 架構

現在您擁有了完整的漸進式升級方案，可以根據自己的節奏安全地升級 Alert 系統！
