# CustomAlert 升級指南

## 🎯 升級概述

已成功將專案中的 CustomAlert 系統從舊版升級到新版，採用現代化的 React Context 架構，同時保持向後兼容性。

## 📁 檔案結構

```
lopokao/
├── components/
│   ├── Alert/
│   │   └── CustomAlert.jsx          # 新版 UI 組件（主要）
│   └── liff/
│       ├── CustomAlert.js           # 舊版組件（保留）
│       └── CustomAlertNew.js        # LIFF 專用新版組件
├── context/
│   └── AlertContext.jsx             # Context Provider
└── test-new-alert.html              # 測試頁面
```

## 🔄 升級內容

### 1. **新版 CustomAlert.jsx**
- ✅ 使用精美的 SVG 圖示
- ✅ 流暢的動畫效果
- ✅ 更好的視覺設計
- ✅ 響應式佈局
- ✅ 無障礙功能支援

### 2. **AlertContext.jsx**
- ✅ React Context API 架構
- ✅ 向後兼容的全域函數
- ✅ useAlert Hook 支援
- ✅ 統一的狀態管理

### 3. **向後兼容性**
- ✅ 保持原有的 `window.showSuccess()` 等函數
- ✅ LIFF 頁面使用專用版本
- ✅ 現有程式碼無需修改

## 🚀 使用方式

### 方式一：全域函數（向後兼容）
```javascript
// 成功訊息
window.showSuccess('訂單提交成功！', '融氏古早味');

// 錯誤訊息
window.showError('訂單提交失敗！', '系統錯誤');

// 警告訊息
window.showWarning('請注意配送時間！', '配送提醒');

// 資訊訊息
window.showInfo('歡迎使用系統！', '系統資訊');
```

### 方式二：React Hook（推薦）
```javascript
import { useAlert } from './context/AlertContext';

function MyComponent() {
    const { showSuccess, showError, showWarning, showInfo } = useAlert();
    
    const handleSubmit = () => {
        showSuccess('操作成功！');
    };
    
    return <button onClick={handleSubmit}>提交</button>;
}
```

### 方式三：Context 直接使用
```javascript
import { useAlert } from './context/AlertContext';

function MyComponent() {
    const { showAlert } = useAlert();
    
    const handleCustomAlert = () => {
        showAlert('自定義訊息', {
            title: '自定義標題',
            type: 'warning',
            onConfirm: () => console.log('確認後執行')
        });
    };
    
    return <button onClick={handleCustomAlert}>自定義彈窗</button>;
}
```

## 🎨 視覺改進

### 舊版 vs 新版對比

| 特性 | 舊版 | 新版 |
|------|------|------|
| 圖示 | Emoji (✅❌⚠️ℹ️) | 精美 SVG 圖示 |
| 動畫 | 基礎 transition | 流暢的進場/退場動畫 |
| 設計 | 基礎樣式 | 現代化設計，圓角、陰影 |
| 關閉方式 | 僅確定按鈕 | 確定按鈕 + 右上角關閉 + 背景點擊 |
| 響應式 | 基本支援 | 完全響應式 |

## 🔧 技術改進

### 架構優化
- **舊版**：直接操作 `window` 物件
- **新版**：React Context + Hook 架構

### 程式碼品質
- **舊版**：單一檔案，混合邏輯
- **新版**：分離關注點，可測試性更好

### 維護性
- **舊版**：難以擴展和維護
- **新版**：模組化，易於擴展

## 📱 LIFF 頁面支援

LIFF 頁面使用專用的 `CustomAlertNew.js`，提供：
- 與新版相同的視覺效果
- 向後兼容的 API
- 適合 script 標籤載入的格式

## 🧪 測試

### 測試頁面
開啟 `test-new-alert.html` 來測試所有功能：
```bash
# 在瀏覽器中開啟
http://localhost/lopokao/test-new-alert.html
```

### 測試項目
- ✅ 成功訊息顯示
- ✅ 錯誤訊息顯示
- ✅ 警告訊息顯示
- ✅ 資訊訊息顯示
- ✅ 長訊息處理
- ✅ 動畫效果
- ✅ 響應式佈局

## 🔄 遷移建議

### 立即可用
- 現有程式碼無需修改
- 全域函數繼續正常工作
- 視覺效果自動升級

### 未來優化
建議逐步將程式碼遷移到 Hook 方式：

```javascript
// 舊方式（仍可用）
window.showSuccess('成功！');

// 新方式（推薦）
const { showSuccess } = useAlert();
showSuccess('成功！');
```

## 🎯 總結

✅ **完成項目**
- 新版 CustomAlert UI 組件
- React Context 架構
- 向後兼容性保持
- LIFF 頁面支援
- 測試頁面建立

✅ **優勢**
- 更美觀的視覺效果
- 更好的使用者體驗
- 更現代的程式碼架構
- 更容易維護和擴展
- 完全向後兼容

現在您可以享受新版 CustomAlert 帶來的所有改進，同時現有功能完全不受影響！
