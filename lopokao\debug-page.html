<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融氏古早味 - 頁面除錯工具</title>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #8b4513 0%, #6d3611 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .status-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .status-ok {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .status-warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .status-error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .icon {
            margin-right: 10px;
            font-size: 18px;
        }
        .button {
            background: #8b4513;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .button:hover {
            background: #6d3611;
        }
        .log-area {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 融氏古早味 - 頁面除錯工具</h1>
            <p>檢測和修復常見的頁面載入問題</p>
        </div>
        
        <div class="content">
            <h2>📊 系統狀態檢查</h2>
            <div id="status-checks"></div>
            
            <h2>🛠️ 快速修復工具</h2>
            <div>
                <button class="button" onclick="clearCache()">清除快取</button>
                <button class="button" onclick="reloadPage()">重新載入</button>
                <button class="button" onclick="testAlert()">測試 Alert</button>
                <button class="button" onclick="checkDependencies()">檢查依賴</button>
                <button class="button" onclick="showConsoleErrors()">顯示錯誤</button>
            </div>
            
            <h2>📝 錯誤日誌</h2>
            <div id="error-log" class="log-area">等待檢查...</div>
            
            <h2>🔍 依賴檢查</h2>
            <div id="dependency-check" class="log-area">等待檢查...</div>
        </div>
    </div>

    <script>
        // 錯誤收集
        const errors = [];
        const originalConsoleError = console.error;
        console.error = function(...args) {
            errors.push({
                type: 'error',
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            originalConsoleError.apply(console, args);
            updateErrorLog();
        };

        // 警告收集
        const originalConsoleWarn = console.warn;
        console.warn = function(...args) {
            errors.push({
                type: 'warning',
                message: args.join(' '),
                timestamp: new Date().toISOString()
            });
            originalConsoleWarn.apply(console, args);
            updateErrorLog();
        };

        // 更新錯誤日誌
        function updateErrorLog() {
            const logArea = document.getElementById('error-log');
            if (errors.length === 0) {
                logArea.innerHTML = '✅ 沒有發現錯誤';
            } else {
                logArea.innerHTML = errors.map(error => 
                    `[${error.timestamp}] ${error.type.toUpperCase()}: ${error.message}`
                ).join('\n');
            }
        }

        // 系統狀態檢查
        function checkSystemStatus() {
            const statusContainer = document.getElementById('status-checks');
            const checks = [
                {
                    name: 'React 載入',
                    check: () => typeof React !== 'undefined',
                    fix: '請確認 React CDN 連結正確'
                },
                {
                    name: 'ReactDOM 載入',
                    check: () => typeof ReactDOM !== 'undefined',
                    fix: '請確認 ReactDOM CDN 連結正確'
                },
                {
                    name: 'Babel 載入',
                    check: () => typeof Babel !== 'undefined',
                    fix: '請確認 Babel CDN 連結正確'
                },
                {
                    name: 'AlertProvider 載入',
                    check: () => typeof window.AlertProvider !== 'undefined',
                    fix: '請確認 AlertContextBrowser.js 已載入'
                },
                {
                    name: 'Alert 升級工具',
                    check: () => typeof window.AlertUpgradeUtils !== 'undefined',
                    fix: '請確認 alertUpgradeUtils.js 已載入'
                },
                {
                    name: '全域 Alert 函數',
                    check: () => typeof window.showSuccess === 'function',
                    fix: 'Alert 系統未正確初始化'
                }
            ];

            statusContainer.innerHTML = checks.map(check => {
                const isOk = check.check();
                const statusClass = isOk ? 'status-ok' : 'status-error';
                const icon = isOk ? '✅' : '❌';
                return `
                    <div class="${statusClass} status-item">
                        <span class="icon">${icon}</span>
                        <div>
                            <strong>${check.name}</strong>
                            ${!isOk ? `<br><small>修復建議: ${check.fix}</small>` : ''}
                        </div>
                    </div>
                `;
            }).join('');
        }

        // 檢查依賴
        function checkDependencies() {
            const depArea = document.getElementById('dependency-check');
            const deps = {
                'React': window.React,
                'ReactDOM': window.ReactDOM,
                'Babel': window.Babel,
                'AlertProvider': window.AlertProvider,
                'useAlert': window.useAlert,
                'AlertUpgradeUtils': window.AlertUpgradeUtils,
                'SafeAlert': window.SafeAlert,
                'showSuccess': window.showSuccess,
                'showError': window.showError,
                'showWarning': window.showWarning,
                'showInfo': window.showInfo
            };

            let output = '依賴檢查結果:\n\n';
            for (const [name, value] of Object.entries(deps)) {
                const status = value ? '✅ 已載入' : '❌ 未載入';
                const type = typeof value;
                output += `${name}: ${status} (${type})\n`;
            }

            depArea.textContent = output;
        }

        // 清除快取
        function clearCache() {
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            localStorage.clear();
            sessionStorage.clear();
            alert('快取已清除，建議重新載入頁面');
        }

        // 重新載入頁面
        function reloadPage() {
            window.location.reload(true);
        }

        // 測試 Alert
        function testAlert() {
            if (window.showSuccess) {
                window.showSuccess('Alert 系統正常運作！', '測試成功');
            } else if (window.SafeAlert) {
                window.SafeAlert.success('使用安全包裝器的 Alert！', '測試成功');
            } else {
                alert('Alert 系統未載入，使用原生 alert');
            }
        }

        // 顯示控制台錯誤
        function showConsoleErrors() {
            updateErrorLog();
        }

        // 頁面載入時執行檢查
        window.addEventListener('load', () => {
            setTimeout(() => {
                checkSystemStatus();
                checkDependencies();
                updateErrorLog();
            }, 1000);
        });

        // 定期更新狀態
        setInterval(() => {
            checkSystemStatus();
        }, 5000);
    </script>
</body>
</html>
