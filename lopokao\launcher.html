<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融氏古早味 - 快速啟動器</title>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background: linear-gradient(135deg, #8b4513 0%, #6d3611 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            padding: 40px;
            text-align: center;
            max-width: 600px;
            width: 100%;
        }
        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }
        h1 {
            color: #8b4513;
            margin-bottom: 10px;
            font-size: 28px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 40px;
            font-size: 16px;
        }
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .button {
            background: linear-gradient(135deg, #8b4513 0%, #6d3611 100%);
            color: white;
            border: none;
            padding: 20px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            text-decoration: none;
            display: block;
            box-shadow: 0 4px 15px rgba(139, 69, 19, 0.3);
        }
        .button:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(139, 69, 19, 0.4);
        }
        .button-emergency {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            box-shadow: 0 4px 15px rgba(220, 38, 38, 0.3);
        }
        .button-emergency:hover {
            box-shadow: 0 8px 25px rgba(220, 38, 38, 0.4);
        }
        .button-success {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
            box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
        }
        .button-success:hover {
            box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
        }
        .status {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            border-left: 5px solid #10b981;
        }
        .status-error {
            border-left-color: #dc2626;
            background: #fef2f2;
        }
        .icon {
            font-size: 24px;
            margin-right: 10px;
        }
        .footer {
            margin-top: 40px;
            color: #666;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🥮</div>
        <h1>融氏古早味</h1>
        <p class="subtitle">快速啟動器 - 解決頁面載入問題</p>
        
        <div id="status-check" class="status">
            <span class="icon">🔍</span>
            正在檢查系統狀態...
        </div>
        
        <div class="button-grid">
            <a href="emergency-fix.html" class="button button-emergency">
                🚨 緊急修復工具<br>
                <small>自動診斷和修復問題</small>
            </a>
            
            <a href="index.html" class="button button-success">
                🏠 主頁面<br>
                <small>進入主要應用程式</small>
            </a>
            
            <a href="test-cdn.html" class="button">
                📡 CDN 測試<br>
                <small>檢查網路連線狀態</small>
            </a>
            
            <a href="debug-page.html" class="button">
                🔧 除錯工具<br>
                <small>詳細系統診斷</small>
            </a>
            
            <a href="test-new-alert.html" class="button">
                🔔 Alert 測試<br>
                <small>測試彈窗功能</small>
            </a>
            
            <a href="liff-order.html" class="button">
                📱 LIFF 頁面<br>
                <small>LINE 訂購系統</small>
            </a>
        </div>
        
        <div class="button-grid">
            <button class="button" onclick="openAll()">
                🚀 開啟所有工具<br>
                <small>一次開啟所有診斷工具</small>
            </button>
            
            <button class="button" onclick="checkSystem()">
                ✅ 重新檢查<br>
                <small>重新檢查系統狀態</small>
            </button>
        </div>
        
        <div class="footer">
            <p>💡 如果頁面空白，請先使用「緊急修復工具」</p>
            <p>🔧 按 F12 開啟開發者工具查看詳細錯誤</p>
        </div>
    </div>

    <script>
        let systemStatus = {
            xampp: false,
            files: false,
            network: false
        };

        function updateStatus(message, isError = false) {
            const statusDiv = document.getElementById('status-check');
            statusDiv.className = isError ? 'status status-error' : 'status';
            statusDiv.innerHTML = `
                <span class="icon">${isError ? '❌' : '✅'}</span>
                ${message}
            `;
        }

        async function checkSystem() {
            updateStatus('正在檢查系統狀態...');
            
            let issues = [];
            
            // 檢查網路連接
            try {
                const response = await fetch('index.html', { method: 'HEAD' });
                if (response.ok) {
                    systemStatus.network = true;
                } else {
                    issues.push('無法訪問本地檔案');
                }
            } catch (error) {
                issues.push('網路連接問題');
            }
            
            // 檢查關鍵檔案
            const files = ['app.js', 'components/AlertContextBrowser.js', 'utils/alertUpgradeUtils.js'];
            let fileCount = 0;
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    if (response.ok) fileCount++;
                } catch (error) {
                    // 檔案不存在或無法訪問
                }
            }
            
            systemStatus.files = fileCount === files.length;
            if (!systemStatus.files) {
                issues.push(`缺少關鍵檔案 (${fileCount}/${files.length})`);
            }
            
            // 顯示結果
            if (issues.length === 0) {
                updateStatus('✅ 系統狀態正常，可以正常使用');
            } else {
                updateStatus(`⚠️ 發現問題：${issues.join(', ')}`, true);
            }
        }

        function openAll() {
            const tools = [
                'emergency-fix.html',
                'test-cdn.html',
                'debug-page.html'
            ];
            
            tools.forEach((tool, index) => {
                setTimeout(() => {
                    window.open(tool, '_blank');
                }, index * 1000);
            });
            
            updateStatus('🚀 正在開啟所有診斷工具...');
        }

        // 頁面載入時自動檢查
        window.addEventListener('load', () => {
            setTimeout(checkSystem, 1000);
        });

        // 添加鍵盤快捷鍵
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey) {
                switch(e.key) {
                    case '1':
                        e.preventDefault();
                        window.open('emergency-fix.html', '_blank');
                        break;
                    case '2':
                        e.preventDefault();
                        window.open('index.html', '_blank');
                        break;
                    case '3':
                        e.preventDefault();
                        window.open('test-cdn.html', '_blank');
                        break;
                }
            }
        });
    </script>
</body>
</html>
