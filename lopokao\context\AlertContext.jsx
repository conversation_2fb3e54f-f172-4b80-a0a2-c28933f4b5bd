import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import CustomAlert from '../components/Alert/CustomAlert'; // 使用新的 UI 組件

// 1. 建立 Context
const AlertContext = createContext();

// 2. 建立 Provider 組件
export function AlertProvider({ children }) {
    const [alertState, setAlertState] = useState({
        isVisible: false,
        title: '',
        message: '',
        type: 'info',
        onConfirm: null,
    });

    const showAlert = useCallback((message, options = {}) => {
        setAlertState({
            isVisible: true,
            message,
            title: options.title || '融氏蘿蔔糕',
            type: options.type || 'info',
            onConfirm: options.onConfirm || null,
        });
    }, []);

    const hideAlert = useCallback(() => {
        if (alertState.onConfirm) {
            alertState.onConfirm();
        }
        setAlertState((prev) => ({ ...prev, isVisible: false }));
    }, [alertState]);

    // 提供便利的快速方法
    const showSuccess = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'success', title, onConfirm }), [showAlert]);
    const showError = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'error', title, onConfirm }), [showAlert]);
    const showWarning = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'warning', title, onConfirm }), [showAlert]);
    const showInfo = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'info', title, onConfirm }), [showAlert]);

    // 向後兼容：註冊全域函數
    useEffect(() => {
        window.customAlert = showAlert;
        window.showSuccess = showSuccess;
        window.showError = showError;
        window.showWarning = showWarning;
        window.showInfo = showInfo;

        return () => {
            delete window.customAlert;
            delete window.showSuccess;
            delete window.showError;
            delete window.showWarning;
            delete window.showInfo;
        };
    }, [showAlert, showSuccess, showError, showWarning, showInfo]);

    const contextValue = {
        showAlert,
        showSuccess,
        showError,
        showWarning,
        showInfo,
    };

    return (
        <AlertContext.Provider value={contextValue}>
            {children}
            <CustomAlert
                isVisible={alertState.isVisible}
                title={alertState.title}
                message={alertState.message}
                type={alertState.type}
                onClose={hideAlert}
            />
        </AlertContext.Provider>
    );
}

// 3. 建立自定義 Hook
export const useAlert = () => {
    const context = useContext(AlertContext);
    if (!context) {
        throw new Error('useAlert 必須在 AlertProvider 內部使用');
    }
    return context;
};