import React, { createContext, useContext, useState, useCallback } from 'react';
import CustomAlert from '../components/liff/CustomAlert'; // 引入分離後的 UI 組件

// 1. 建立 Context
const AlertContext = createContext();

// 2. 建立 Provider 組件
export function AlertProvider({ children }) {
  const [alertState, setAlertState] = useState({
    isVisible: false,
    title: '',
    message: '',
    type: 'info',
    onConfirm: null,
  });

  const showAlert = useCallback((message, options = {}) => {
    setAlertState({
      isVisible: true,
      message,
      title: options.title || '融氏蘿蔔糕',
      type: options.type || 'info',
      onConfirm: options.onConfirm || null,
    });
  }, []);

  const hideAlert = useCallback(() => {
    if (alertState.onConfirm) {
      alertState.onConfirm();
    }
    setAlertState((prev) => ({ ...prev, isVisible: false }));
  }, [alertState]);

  // 提供便利的快速方法
  const showSuccess = (message, title, onConfirm) => showAlert(message, { type: 'success', title, onConfirm });
  const showError = (message, title, onConfirm) => showAlert(message, { type: 'error', title, onConfirm });
  const showWarning = (message, title, onConfirm) => showAlert(message, { type: 'warning', title, onConfirm });
  const showInfo = (message, title, onConfirm) => showAlert(message, { type: 'info', title, onConfirm });


  const contextValue = {
    showAlert,
    showSuccess,
    showError,
    showWarning,
    showInfo,
  };

  return (
    <AlertContext.Provider value={contextValue}>
      {children}
      <CustomAlert {...alertState} onClose={hideAlert} />
    </AlertContext.Provider>
  );
}

// 3. 建立自定義 Hook
export const useAlert = () => {
  const context = useContext(AlertContext);
  if (!context) {
    throw new Error('useAlert 必須在 AlertProvider 內部使用');
  }
  return context;
};