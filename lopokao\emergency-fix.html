<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>融氏古早味 - 緊急修復工具</title>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
        .status {
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 5px solid #ddd;
            display: flex;
            align-items: center;
        }
        .status-ok {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .status-warning {
            background: #fffbeb;
            border-left-color: #f59e0b;
        }
        .status-error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .icon {
            margin-right: 15px;
            font-size: 24px;
        }
        .button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s;
        }
        .button:hover {
            background: #b91c1c;
            transform: translateY(-2px);
        }
        .button-success {
            background: #10b981;
        }
        .button-success:hover {
            background: #059669;
        }
        .log-area {
            background: #1f2937;
            color: #f9fafb;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            border: 2px solid #374151;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e5e7eb;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #10b981, #059669);
            width: 0%;
            transition: width 0.3s ease;
        }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚨 緊急修復工具</h1>
            <p>自動診斷和修復頁面載入問題</p>
        </div>
        
        <div class="content">
            <div class="grid">
                <div>
                    <h2>🔍 快速診斷</h2>
                    <button class="button" onclick="runFullDiagnosis()">完整診斷</button>
                    <button class="button" onclick="testCDNs()">測試 CDN</button>
                    <button class="button" onclick="checkDependencies()">檢查依賴</button>
                </div>
                
                <div>
                    <h2>🛠️ 快速修復</h2>
                    <button class="button button-success" onclick="emergencyFix()">一鍵修復</button>
                    <button class="button" onclick="loadBackupCDN()">載入備用 CDN</button>
                    <button class="button" onclick="clearAllCache()">清除所有快取</button>
                </div>
            </div>
            
            <h2>📊 診斷進度</h2>
            <div class="progress-bar">
                <div class="progress-fill" id="progress"></div>
            </div>
            <div id="progress-text">等待開始...</div>
            
            <h2>📋 診斷結果</h2>
            <div id="status-results"></div>
            
            <h2>📝 詳細日誌</h2>
            <div id="log-output" class="log-area">等待診斷...</div>
            
            <div style="text-align: center; margin-top: 30px;">
                <button class="button button-success" onclick="goToMainSite()">前往主網站</button>
                <button class="button" onclick="location.reload()">重新載入此頁</button>
            </div>
        </div>
    </div>

    <script>
        let logMessages = [];
        let diagnosticResults = [];
        let currentProgress = 0;
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            logMessages.push(logEntry);
            
            const logOutput = document.getElementById('log-output');
            logOutput.textContent = logMessages.join('\n');
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(logEntry);
        }
        
        function updateProgress(percent, text) {
            currentProgress = percent;
            document.getElementById('progress').style.width = percent + '%';
            document.getElementById('progress-text').textContent = text;
        }
        
        function addResult(name, status, message) {
            diagnosticResults.push({ name, status, message });
            updateResultsDisplay();
        }
        
        function updateResultsDisplay() {
            const container = document.getElementById('status-results');
            container.innerHTML = diagnosticResults.map(result => {
                const statusClass = result.status ? 'status-ok' : 'status-error';
                const icon = result.status ? '✅' : '❌';
                return `
                    <div class="${statusClass} status">
                        <span class="icon">${icon}</span>
                        <div>
                            <strong>${result.name}</strong><br>
                            <small>${result.message}</small>
                        </div>
                    </div>
                `;
            }).join('');
        }
        
        async function runFullDiagnosis() {
            log('開始完整診斷...', 'info');
            diagnosticResults = [];
            updateProgress(0, '開始診斷...');
            
            // 步驟 1: 檢查基本環境
            updateProgress(10, '檢查基本環境...');
            await checkBasicEnvironment();
            
            // 步驟 2: 測試網路連接
            updateProgress(30, '測試網路連接...');
            await testNetworkConnection();
            
            // 步驟 3: 檢查 CDN
            updateProgress(50, '檢查 CDN 狀態...');
            await testCDNs();
            
            // 步驟 4: 檢查依賴
            updateProgress(70, '檢查依賴載入...');
            await checkDependencies();
            
            // 步驟 5: 測試功能
            updateProgress(90, '測試功能...');
            await testFunctionality();
            
            updateProgress(100, '診斷完成！');
            log('完整診斷完成', 'success');
        }
        
        async function checkBasicEnvironment() {
            log('檢查基本環境...', 'info');
            
            addResult('瀏覽器支援', true, `${navigator.userAgent.split(' ')[0]}`);
            addResult('JavaScript', true, '已啟用');
            addResult('本地存儲', typeof localStorage !== 'undefined', 
                typeof localStorage !== 'undefined' ? '可用' : '不可用');
            addResult('會話存儲', typeof sessionStorage !== 'undefined',
                typeof sessionStorage !== 'undefined' ? '可用' : '不可用');
        }
        
        async function testNetworkConnection() {
            log('測試網路連接...', 'info');
            
            try {
                const response = await fetch('https://httpbin.org/get', { 
                    method: 'GET',
                    mode: 'cors'
                });
                addResult('網路連接', response.ok, `狀態碼: ${response.status}`);
                log('網路連接正常', 'success');
            } catch (error) {
                addResult('網路連接', false, `錯誤: ${error.message}`);
                log(`網路連接失敗: ${error.message}`, 'error');
            }
        }
        
        async function testCDNs() {
            log('測試 CDN 連接...', 'info');
            
            const cdns = [
                { name: 'unpkg.com', url: 'https://unpkg.com/react@18/package.json' },
                { name: 'jsdelivr.net', url: 'https://cdn.jsdelivr.net/npm/react@18/package.json' },
                { name: 'cdnjs.com', url: 'https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/package.json' }
            ];
            
            for (const cdn of cdns) {
                try {
                    const response = await fetch(cdn.url);
                    const data = await response.json();
                    addResult(`CDN: ${cdn.name}`, response.ok, `React ${data.version || '未知版本'}`);
                    log(`${cdn.name} 可用`, 'success');
                } catch (error) {
                    addResult(`CDN: ${cdn.name}`, false, `無法連接: ${error.message}`);
                    log(`${cdn.name} 不可用: ${error.message}`, 'error');
                }
            }
        }
        
        async function checkDependencies() {
            log('檢查依賴載入...', 'info');
            
            const deps = {
                'React': window.React,
                'ReactDOM': window.ReactDOM,
                'Babel': window.Babel
            };
            
            for (const [name, value] of Object.entries(deps)) {
                const isLoaded = typeof value !== 'undefined';
                const version = isLoaded && value.version ? value.version : '未知版本';
                addResult(`依賴: ${name}`, isLoaded, isLoaded ? `版本: ${version}` : '未載入');
                log(`${name}: ${isLoaded ? '已載入' : '未載入'}`, isLoaded ? 'success' : 'error');
            }
        }
        
        async function testFunctionality() {
            log('測試功能...', 'info');
            
            if (typeof React !== 'undefined') {
                try {
                    const element = React.createElement('div', null, 'Test');
                    addResult('React 功能', true, 'createElement 正常');
                    log('React 功能測試通過', 'success');
                } catch (error) {
                    addResult('React 功能', false, `錯誤: ${error.message}`);
                    log(`React 功能測試失敗: ${error.message}`, 'error');
                }
            }
        }
        
        async function emergencyFix() {
            log('開始緊急修復...', 'info');
            updateProgress(0, '開始修復...');
            
            // 清除快取
            updateProgress(25, '清除快取...');
            await clearAllCache();
            
            // 載入備用 CDN
            updateProgress(50, '載入備用 CDN...');
            await loadBackupCDN();
            
            // 等待載入
            updateProgress(75, '等待載入完成...');
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // 重新檢查
            updateProgress(100, '修復完成！');
            log('緊急修復完成，正在重新檢查...', 'success');
            
            setTimeout(() => {
                runFullDiagnosis();
            }, 1000);
        }
        
        async function loadBackupCDN() {
            log('載入備用 CDN...', 'info');
            
            const scripts = [
                'https://cdn.jsdelivr.net/npm/react@18/umd/react.development.js',
                'https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.development.js',
                'https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js'
            ];
            
            for (const src of scripts) {
                try {
                    await loadScript(src);
                    log(`成功載入: ${src}`, 'success');
                } catch (error) {
                    log(`載入失敗: ${src}`, 'error');
                }
            }
        }
        
        function loadScript(src) {
            return new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = src;
                script.crossOrigin = 'anonymous';
                script.onload = resolve;
                script.onerror = reject;
                document.head.appendChild(script);
            });
        }
        
        async function clearAllCache() {
            log('清除所有快取...', 'info');
            
            // 清除瀏覽器快取
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                await Promise.all(cacheNames.map(name => caches.delete(name)));
                log('Service Worker 快取已清除', 'success');
            }
            
            // 清除本地存儲
            localStorage.clear();
            sessionStorage.clear();
            log('本地存儲已清除', 'success');
        }
        
        function goToMainSite() {
            window.location.href = 'index.html';
        }
        
        // 頁面載入時自動開始診斷
        window.addEventListener('load', () => {
            log('緊急修復工具已載入', 'info');
            setTimeout(runFullDiagnosis, 1000);
        });
    </script>
</body>
</html>
