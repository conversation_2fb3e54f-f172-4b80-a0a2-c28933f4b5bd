/**
 * Alert 系統升級工具
 * 提供漸進式升級的檢測和遷移功能
 */

/**
 * 檢查 Alert 系統是否已升級
 * @returns {boolean} 是否已升級到新版本
 */
function isAlertSystemUpgraded() {
    return window._alertSystemUpgraded === true;
}

/**
 * 獲取當前 Alert 系統版本
 * @returns {string} 版本號
 */
function getAlertSystemVersion() {
    return window._alertVersion || '1.0';
}

/**
 * 檢查是否支援 useAlert Hook
 * @returns {boolean} 是否支援 Hook
 */
function supportsAlertHook() {
    return typeof window.React !== 'undefined' && 
           typeof window.React.useContext !== 'undefined' &&
           isAlertSystemUpgraded();
}

/**
 * 安全的 Alert 調用包裝器
 * 自動檢測並使用最佳可用的 Alert 方法
 */
const SafeAlert = {
    /**
     * 顯示成功訊息
     * @param {string} message 訊息內容
     * @param {string} title 標題
     * @param {function} onConfirm 確認回調
     */
    success: (message, title = '融氏蘿蔔糕', onConfirm = null) => {
        if (window.showSuccess) {
            window.showSuccess(message, title, onConfirm);
        } else {
            console.warn('Alert system not available, falling back to native alert');
            alert(`${title}\n\n${message}`);
            if (onConfirm) onConfirm();
        }
    },

    /**
     * 顯示錯誤訊息
     * @param {string} message 訊息內容
     * @param {string} title 標題
     * @param {function} onConfirm 確認回調
     */
    error: (message, title = '融氏蘿蔔糕', onConfirm = null) => {
        if (window.showError) {
            window.showError(message, title, onConfirm);
        } else {
            console.warn('Alert system not available, falling back to native alert');
            alert(`${title}\n\n${message}`);
            if (onConfirm) onConfirm();
        }
    },

    /**
     * 顯示警告訊息
     * @param {string} message 訊息內容
     * @param {string} title 標題
     * @param {function} onConfirm 確認回調
     */
    warning: (message, title = '融氏蘿蔔糕', onConfirm = null) => {
        if (window.showWarning) {
            window.showWarning(message, title, onConfirm);
        } else {
            console.warn('Alert system not available, falling back to native alert');
            alert(`${title}\n\n${message}`);
            if (onConfirm) onConfirm();
        }
    },

    /**
     * 顯示資訊訊息
     * @param {string} message 訊息內容
     * @param {string} title 標題
     * @param {function} onConfirm 確認回調
     */
    info: (message, title = '融氏蘿蔔糕', onConfirm = null) => {
        if (window.showInfo) {
            window.showInfo(message, title, onConfirm);
        } else {
            console.warn('Alert system not available, falling back to native alert');
            alert(`${title}\n\n${message}`);
            if (onConfirm) onConfirm();
        }
    },

    /**
     * 自定義 Alert
     * @param {string} message 訊息內容
     * @param {object} options 選項
     */
    custom: (message, options = {}) => {
        if (window.customAlert) {
            window.customAlert(message, options);
        } else {
            console.warn('Custom alert not available, falling back to native alert');
            alert(`${options.title || '融氏蘿蔔糕'}\n\n${message}`);
            if (options.onConfirm) options.onConfirm();
        }
    }
};

/**
 * 升級狀態報告
 * @returns {object} 升級狀態詳情
 */
function getUpgradeStatus() {
    return {
        isUpgraded: isAlertSystemUpgraded(),
        version: getAlertSystemVersion(),
        supportsHook: supportsAlertHook(),
        availableFunctions: {
            customAlert: typeof window.customAlert === 'function',
            showSuccess: typeof window.showSuccess === 'function',
            showError: typeof window.showError === 'function',
            showWarning: typeof window.showWarning === 'function',
            showInfo: typeof window.showInfo === 'function'
        },
        recommendations: getUpgradeRecommendations()
    };
}

/**
 * 獲取升級建議
 * @returns {array} 建議列表
 */
function getUpgradeRecommendations() {
    const recommendations = [];
    
    if (!isAlertSystemUpgraded()) {
        recommendations.push('建議升級到新版 Alert 系統以獲得更好的使用者體驗');
    }
    
    if (!supportsAlertHook()) {
        recommendations.push('建議在 React 組件中使用 useAlert Hook 替代全域函數');
    }
    
    if (getAlertSystemVersion() === '1.0') {
        recommendations.push('建議更新到最新版本以獲得最新功能和修復');
    }
    
    return recommendations;
}

/**
 * 顯示升級狀態報告
 */
function showUpgradeReport() {
    const status = getUpgradeStatus();
    
    console.group('🔄 Alert 系統升級狀態報告');
    console.log('📊 升級狀態:', status.isUpgraded ? '✅ 已升級' : '❌ 未升級');
    console.log('📦 版本:', status.version);
    console.log('🎣 Hook 支援:', status.supportsHook ? '✅ 支援' : '❌ 不支援');
    console.log('🔧 可用函數:', status.availableFunctions);
    
    if (status.recommendations.length > 0) {
        console.log('💡 建議:');
        status.recommendations.forEach((rec, index) => {
            console.log(`   ${index + 1}. ${rec}`);
        });
    }
    
    console.groupEnd();
    
    return status;
}

// 全域導出
window.AlertUpgradeUtils = {
    isAlertSystemUpgraded,
    getAlertSystemVersion,
    supportsAlertHook,
    SafeAlert,
    getUpgradeStatus,
    showUpgradeReport
};

// 便利的全域別名
window.SafeAlert = SafeAlert;
