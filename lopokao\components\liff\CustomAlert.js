/**
 * LIFF 訂購系統 - 自定義警告彈窗組件
 * 用於替換原生 alert()，提供自定義標題和更好的用戶體驗
 */

function CustomAlert() {
    const [isVisible, setIsVisible] = useState(false);
    const [alertData, setAlertData] = useState({
        title: '',
        message: '',
        type: 'info', // 'success', 'error', 'warning', 'info'
        onConfirm: null
    });

    // 全域函數：顯示自定義警告
    const showAlert = (message, title = '融氏蘿蔔糕', type = 'info', onConfirm = null) => {
        setAlertData({
            title,
            message,
            type,
            onConfirm
        });
        setIsVisible(true);
    };

    // 確認按鈕處理
    const handleConfirm = () => {
        setIsVisible(false);
        if (alertData.onConfirm) {
            alertData.onConfirm();
        }
    };

    // 獲取圖標和顏色
    const getAlertStyle = (type) => {
        switch (type) {
            case 'success':
                return {
                    icon: '✅',
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                    iconColor: 'text-green-600',
                    titleColor: 'text-green-800',
                    buttonColor: 'bg-green-500 hover:bg-green-600'
                };
            case 'error':
                return {
                    icon: '❌',
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    iconColor: 'text-red-600',
                    titleColor: 'text-red-800',
                    buttonColor: 'bg-red-500 hover:bg-red-600'
                };
            case 'warning':
                return {
                    icon: '⚠️',
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    iconColor: 'text-yellow-600',
                    titleColor: 'text-yellow-800',
                    buttonColor: 'bg-yellow-500 hover:bg-yellow-600'
                };
            default: // info
                return {
                    icon: 'ℹ️',
                    bgColor: 'bg-blue-50',
                    borderColor: 'border-blue-200',
                    iconColor: 'text-blue-600',
                    titleColor: 'text-blue-800',
                    buttonColor: 'bg-blue-500 hover:bg-blue-600'
                };
        }
    };

    // 註冊全域函數
    useEffect(() => {
        window.customAlert = showAlert;
        
        // 提供便利的快速方法
        window.showSuccess = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'success', onConfirm);
        window.showError = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'error', onConfirm);
        window.showWarning = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'warning', onConfirm);
        window.showInfo = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'info', onConfirm);
            
        return () => {
            delete window.customAlert;
            delete window.showSuccess;
            delete window.showError;
            delete window.showWarning;
            delete window.showInfo;
        };
    }, []);

    if (!isVisible) return null;

    const style = getAlertStyle(alertData.type);

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" style={{ margin: '-20px' }}>
            <div className="bg-white rounded-xl max-w-sm mx-4 overflow-hidden shadow-2xl transform transition-all">
                {/* 標題列 */}
                <div className={`${style.bgColor} ${style.borderColor} border-b px-6 py-4`}>
                    <div className="flex items-center">
                        <span className={`text-xl mr-3 ${style.iconColor}`}>
                            {style.icon}
                        </span>
                        <h3 className={`text-xl font-bold ${style.titleColor}`}>
                            {alertData.title}
                        </h3>
                    </div>
                </div>
                
                {/* 內容區域 */}
                <div className="px-6 py-6">
                    <div className="text-gray-700 text-2xl leading-relaxed whitespace-pre-line">
                        {alertData.message}
                    </div>
                </div>
                
                {/* 按鈕區域 */}
                <div className="px-6 py-4 bg-gray-50 flex justify-end">
                    <button
                        onClick={handleConfirm}
                        className={`px-6 py-3 ${style.buttonColor} text-white font-medium rounded-lg transition-colors duration-200 min-w-20`}
                    >
                        確定
                    </button>
                </div>
            </div>
        </div>
    );
}

// 全域導出
window.CustomAlert = CustomAlert;