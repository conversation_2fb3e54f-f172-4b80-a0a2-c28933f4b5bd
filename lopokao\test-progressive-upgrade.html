<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Alert 系統漸進式升級測試</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #8b4513 0%, #6d3611 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .content {
            padding: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <div class="header">
                <h1 class="text-3xl font-bold mb-2">融氏古早味</h1>
                <h2 class="text-xl opacity-90">Alert 系統漸進式升級測試</h2>
                <p class="mt-4 opacity-80">展示如何在不中斷現有功能的情況下升級 Alert 系統</p>
            </div>
            
            <div class="content">
                <!-- 升級狀態面板 -->
                <div id="upgrade-status" class="mb-8"></div>
                
                <!-- 主要示範區域 -->
                <div id="main-demo"></div>
                
                <!-- Alert 容器 -->
                <div id="alert-container"></div>
            </div>
        </div>
    </div>

    <!-- 載入升級工具 -->
    <script src="./utils/alertUpgradeUtils.js"></script>
    
    <!-- 載入新版 AlertContext -->
    <script type="text/babel">
        // 由於這是測試頁面，我們需要手動導入 AlertContext
        // 在實際應用中，這會通過模組系統自動處理
        
        const { useState, useEffect, useContext, createContext, useCallback } = React;
        
        // 複製 AlertContext 的核心邏輯（簡化版）
        const AlertContext = createContext();
        
        function AlertProvider({ children }) {
            const [alertState, setAlertState] = useState({
                isVisible: false,
                title: '',
                message: '',
                type: 'info',
                onConfirm: null,
            });

            const showAlert = useCallback((message, options = {}) => {
                setAlertState({
                    isVisible: true,
                    message,
                    title: options.title || '融氏蘿蔔糕',
                    type: options.type || 'info',
                    onConfirm: options.onConfirm || null,
                });
            }, []);

            const hideAlert = useCallback(() => {
                if (alertState.onConfirm) {
                    alertState.onConfirm();
                }
                setAlertState((prev) => ({ ...prev, isVisible: false }));
            }, [alertState]);

            const showSuccess = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
                showAlert(message, { type: 'success', title, onConfirm }), [showAlert]);
            const showError = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
                showAlert(message, { type: 'error', title, onConfirm }), [showAlert]);
            const showWarning = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
                showAlert(message, { type: 'warning', title, onConfirm }), [showAlert]);
            const showInfo = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
                showAlert(message, { type: 'info', title, onConfirm }), [showAlert]);

            // 註冊全域函數
            useEffect(() => {
                window.customAlert = showAlert;
                window.showSuccess = showSuccess;
                window.showError = showError;
                window.showWarning = showWarning;
                window.showInfo = showInfo;
                window._alertSystemUpgraded = true;
                window._alertVersion = '2.0';

                return () => {
                    delete window.customAlert;
                    delete window.showSuccess;
                    delete window.showError;
                    delete window.showWarning;
                    delete window.showInfo;
                    delete window._alertSystemUpgraded;
                    delete window._alertVersion;
                };
            }, [showAlert, showSuccess, showError, showWarning, showInfo]);

            const contextValue = {
                showAlert,
                showSuccess,
                showError,
                showWarning,
                showInfo,
            };

            return React.createElement(AlertContext.Provider, { value: contextValue }, [
                children,
                React.createElement(SimpleCustomAlert, {
                    key: 'alert',
                    isVisible: alertState.isVisible,
                    title: alertState.title,
                    message: alertState.message,
                    type: alertState.type,
                    onClose: hideAlert
                })
            ]);
        }
        
        // 簡化版的 CustomAlert 組件
        function SimpleCustomAlert({ isVisible, title, message, type, onClose }) {
            if (!isVisible) return null;
            
            const getTheme = (type) => {
                switch (type) {
                    case 'success': return { bg: 'bg-green-50', border: 'border-green-200', text: 'text-green-800', button: 'bg-green-600 hover:bg-green-700' };
                    case 'error': return { bg: 'bg-red-50', border: 'border-red-200', text: 'text-red-800', button: 'bg-red-600 hover:bg-red-700' };
                    case 'warning': return { bg: 'bg-yellow-50', border: 'border-yellow-200', text: 'text-yellow-800', button: 'bg-yellow-600 hover:bg-yellow-700' };
                    default: return { bg: 'bg-blue-50', border: 'border-blue-200', text: 'text-blue-800', button: 'bg-blue-600 hover:bg-blue-700' };
                }
            };
            
            const theme = getTheme(type);
            
            return React.createElement('div', {
                className: 'fixed inset-0 z-50 flex items-center justify-center'
            }, [
                React.createElement('div', {
                    key: 'backdrop',
                    className: 'absolute inset-0 bg-black opacity-60',
                    onClick: onClose
                }),
                React.createElement('div', {
                    key: 'modal',
                    className: `relative z-10 w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden`
                }, [
                    React.createElement('div', {
                        key: 'header',
                        className: `p-5 ${theme.bg} border-b ${theme.border}`
                    }, React.createElement('h3', {
                        className: `text-lg font-semibold ${theme.text}`
                    }, title)),
                    React.createElement('div', {
                        key: 'content',
                        className: 'p-6'
                    }, React.createElement('p', {
                        className: 'text-base text-gray-700 leading-relaxed whitespace-pre-wrap'
                    }, message)),
                    React.createElement('div', {
                        key: 'footer',
                        className: 'px-6 py-4 bg-gray-50 flex justify-end'
                    }, React.createElement('button', {
                        onClick: onClose,
                        className: `px-8 py-2.5 text-white font-semibold rounded-lg transition-all duration-200 ${theme.button}`
                    }, '確定'))
                ])
            ]);
        }
        
        // 使 useAlert 可用
        window.useAlert = () => {
            const context = useContext(AlertContext);
            if (!context) {
                throw new Error('useAlert 必須在 AlertProvider 內部使用');
            }
            return context;
        };
        
        // 升級狀態面板組件
        function UpgradeStatusPanel() {
            const [status, setStatus] = useState(null);
            
            useEffect(() => {
                if (window.AlertUpgradeUtils) {
                    setStatus(window.AlertUpgradeUtils.getUpgradeStatus());
                }
            }, []);
            
            if (!status) return null;
            
            return React.createElement('div', {
                className: 'bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-6 mb-6'
            }, [
                React.createElement('h3', {
                    key: 'title',
                    className: 'text-xl font-bold text-blue-800 mb-4'
                }, '🔄 系統升級狀態'),
                
                React.createElement('div', {
                    key: 'grid',
                    className: 'grid grid-cols-1 md:grid-cols-3 gap-4 mb-4'
                }, [
                    React.createElement('div', {
                        key: 'version',
                        className: 'text-center p-3 bg-white rounded-lg shadow-sm'
                    }, [
                        React.createElement('div', { key: 'v-label', className: 'text-sm text-gray-600' }, '版本'),
                        React.createElement('div', { key: 'v-value', className: 'text-lg font-bold text-blue-600' }, status.version)
                    ]),
                    
                    React.createElement('div', {
                        key: 'upgraded',
                        className: 'text-center p-3 bg-white rounded-lg shadow-sm'
                    }, [
                        React.createElement('div', { key: 'u-label', className: 'text-sm text-gray-600' }, '升級狀態'),
                        React.createElement('div', { 
                            key: 'u-value', 
                            className: `text-lg font-bold ${status.isUpgraded ? 'text-green-600' : 'text-red-600'}`
                        }, status.isUpgraded ? '✅ 已升級' : '❌ 未升級')
                    ]),
                    
                    React.createElement('div', {
                        key: 'hook',
                        className: 'text-center p-3 bg-white rounded-lg shadow-sm'
                    }, [
                        React.createElement('div', { key: 'h-label', className: 'text-sm text-gray-600' }, 'Hook 支援'),
                        React.createElement('div', { 
                            key: 'h-value', 
                            className: `text-lg font-bold ${status.supportsHook ? 'text-green-600' : 'text-orange-600'}`
                        }, status.supportsHook ? '✅ 支援' : '⚠️ 有限')
                    ])
                ]),
                
                status.recommendations.length > 0 && React.createElement('div', {
                    key: 'recommendations',
                    className: 'bg-yellow-50 border border-yellow-200 rounded-lg p-4'
                }, [
                    React.createElement('h4', {
                        key: 'rec-title',
                        className: 'font-semibold text-yellow-800 mb-2'
                    }, '💡 升級建議'),
                    React.createElement('ul', {
                        key: 'rec-list',
                        className: 'text-sm text-yellow-700 space-y-1'
                    }, status.recommendations.map((rec, index) => 
                        React.createElement('li', { key: index }, `• ${rec}`)
                    ))
                ])
            ]);
        }
        
        // 主應用組件
        function TestApp() {
            return React.createElement(AlertProvider, {}, [
                React.createElement(UpgradeStatusPanel, { key: 'status' }),
                React.createElement(AlertMigrationExample, { key: 'demo' })
            ]);
        }
        
        // 等待 AlertMigrationExample 載入後再渲染
        function initApp() {
            if (window.AlertMigrationExample) {
                ReactDOM.render(React.createElement(TestApp), document.getElementById('main-demo'));
            } else {
                setTimeout(initApp, 100);
            }
        }
        
        initApp();
    </script>
    
    <!-- 載入示範組件 -->
    <script type="text/babel" src="./components/examples/AlertMigrationExample.js"></script>
</body>
</html>
