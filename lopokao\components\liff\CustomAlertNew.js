/**
 * LIFF 訂購系統 - 新版自定義警告彈窗組件
 * 使用新的 UI 設計，但保持向後兼容的 API
 */

// 精美的 SVG 圖示組件
const CheckCircleIcon = () => React.createElement('svg', {
    className: "w-6 h-6",
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24"
}, React.createElement('path', {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
}));

const XCircleIcon = () => React.createElement('svg', {
    className: "w-6 h-6",
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24"
}, React.createElement('path', {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
}));

const ExclamationTriangleIcon = () => React.createElement('svg', {
    className: "w-6 h-6",
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24"
}, React.createElement('path', {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
}));

const InformationCircleIcon = () => React.createElement('svg', {
    className: "w-6 h-6",
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24"
}, React.createElement('path', {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
}));

const CloseIcon = () => React.createElement('svg', {
    className: "w-5 h-5",
    fill: "none",
    stroke: "currentColor",
    viewBox: "0 0 24 24"
}, React.createElement('path', {
    strokeLinecap: "round",
    strokeLinejoin: "round",
    strokeWidth: 2,
    d: "M6 18L18 6M6 6l12 12"
}));

// 獲取圖標和顏色主題
const getAlertTheme = (type) => {
    switch (type) {
        case 'success':
            return {
                Icon: CheckCircleIcon,
                bgColor: 'bg-green-50',
                borderColor: 'border-green-200',
                iconBg: 'bg-green-100',
                iconColor: 'text-green-600',
                titleColor: 'text-green-900',
                buttonColor: 'bg-green-600 hover:bg-green-700 focus:ring-green-500'
            };
        case 'error':
            return {
                Icon: XCircleIcon,
                bgColor: 'bg-red-50',
                borderColor: 'border-red-200',
                iconBg: 'bg-red-100',
                iconColor: 'text-red-600',
                titleColor: 'text-red-900',
                buttonColor: 'bg-red-600 hover:bg-red-700 focus:ring-red-500'
            };
        case 'warning':
            return {
                Icon: ExclamationTriangleIcon,
                bgColor: 'bg-yellow-50',
                borderColor: 'border-yellow-200',
                iconBg: 'bg-yellow-100',
                iconColor: 'text-yellow-600',
                titleColor: 'text-yellow-900',
                buttonColor: 'bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500'
            };
        default:
            return {
                Icon: InformationCircleIcon,
                bgColor: 'bg-blue-50',
                borderColor: 'border-blue-200',
                iconBg: 'bg-blue-100',
                iconColor: 'text-blue-600',
                titleColor: 'text-blue-900',
                buttonColor: 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
            };
    }
};

function CustomAlert() {
    const [isVisible, setIsVisible] = useState(false);
    const [alertData, setAlertData] = useState({
        title: '',
        message: '',
        type: 'info',
        onConfirm: null
    });

    // 全域函數：顯示自定義警告
    const showAlert = (message, title = '融氏蘿蔔糕', type = 'info', onConfirm = null) => {
        setAlertData({
            title,
            message,
            type,
            onConfirm
        });
        setIsVisible(true);
    };

    // 確認按鈕處理
    const handleConfirm = () => {
        setIsVisible(false);
        if (alertData.onConfirm) {
            alertData.onConfirm();
        }
    };

    // 註冊全域函數
    useEffect(() => {
        window.customAlert = showAlert;
        
        // 提供便利的快速方法
        window.showSuccess = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'success', onConfirm);
        window.showError = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'error', onConfirm);
        window.showWarning = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'warning', onConfirm);
        window.showInfo = (message, title = '融氏蘿蔔糕', onConfirm) => 
            showAlert(message, title, 'info', onConfirm);
            
        return () => {
            delete window.customAlert;
            delete window.showSuccess;
            delete window.showError;
            delete window.showWarning;
            delete window.showInfo;
        };
    }, []);

    if (!isVisible) return null;

    const theme = getAlertTheme(alertData.type);

    return React.createElement('div', {
        className: "fixed inset-0 z-50 flex items-center justify-center"
    }, [
        // 背景遮罩
        React.createElement('div', {
            key: 'backdrop',
            className: `absolute inset-0 bg-black transition-opacity duration-300 ${isVisible ? 'opacity-60' : 'opacity-0'}`,
            onClick: handleConfirm
        }),
        
        // 彈窗主體
        React.createElement('div', {
            key: 'modal',
            className: `relative z-10 w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden transform transition-all duration-300 ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`
        }, [
            // 標題和圖示
            React.createElement('div', {
                key: 'header',
                className: `flex items-center p-5 ${theme.bgColor} border-b ${theme.borderColor}`
            }, [
                React.createElement('div', {
                    key: 'icon',
                    className: `flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full ${theme.iconBg} ${theme.iconColor}`
                }, React.createElement(theme.Icon)),
                
                React.createElement('div', {
                    key: 'title-container',
                    className: "ml-4 flex-1"
                }, React.createElement('h3', {
                    className: `text-lg font-semibold ${theme.titleColor}`
                }, alertData.title)),
                
                React.createElement('button', {
                    key: 'close-btn',
                    onClick: handleConfirm,
                    className: "flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100",
                    'aria-label': "關閉"
                }, React.createElement(CloseIcon))
            ]),
            
            // 內容
            React.createElement('div', {
                key: 'content',
                className: "p-6"
            }, React.createElement('p', {
                className: "text-base text-gray-700 leading-relaxed whitespace-pre-wrap break-words"
            }, alertData.message)),
            
            // 按鈕
            React.createElement('div', {
                key: 'footer',
                className: "px-6 py-4 bg-gray-50 flex justify-end"
            }, React.createElement('button', {
                onClick: handleConfirm,
                className: `px-8 py-2.5 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${theme.buttonColor}`
            }, '確定'))
        ])
    ]);
}

// 全域導出
window.CustomAlert = CustomAlert;
