/**
 * CDN 備用載入方案
 * 當主要 CDN 不可用時，自動嘗試備用 CDN
 */

(function() {
    'use strict';
    
    // CDN 來源配置
    const CDN_SOURCES = {
        react: [
            'https://unpkg.com/react@18/umd/react.development.js',
            'https://cdn.jsdelivr.net/npm/react@18/umd/react.development.js',
            'https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/umd/react.development.js'
        ],
        reactDOM: [
            'https://unpkg.com/react-dom@18/umd/react-dom.development.js',
            'https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.development.js',
            'https://cdnjs.cloudflare.com/ajax/libs/react-dom/18.2.0/umd/react-dom.development.js'
        ],
        babel: [
            'https://unpkg.com/@babel/standalone/babel.min.js',
            'https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js',
            'https://cdnjs.cloudflare.com/ajax/libs/babel-standalone/7.22.5/babel.min.js'
        ]
    };
    
    // 載入狀態追蹤
    const loadStatus = {
        react: false,
        reactDOM: false,
        babel: false
    };
    
    // 載入腳本的 Promise 包裝器
    function loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.crossOrigin = 'anonymous';
            
            script.onload = () => {
                console.log(`✅ 成功載入: ${src}`);
                resolve(src);
            };
            
            script.onerror = () => {
                console.warn(`❌ 載入失敗: ${src}`);
                reject(new Error(`Failed to load ${src}`));
            };
            
            // 設定超時
            setTimeout(() => {
                if (!script.onload.called) {
                    console.warn(`⏰ 載入超時: ${src}`);
                    reject(new Error(`Timeout loading ${src}`));
                }
            }, 10000); // 10秒超時
            
            document.head.appendChild(script);
        });
    }
    
    // 嘗試載入庫，如果失敗則嘗試下一個 CDN
    async function loadLibraryWithFallback(libraryName, urls) {
        for (let i = 0; i < urls.length; i++) {
            try {
                await loadScript(urls[i]);
                loadStatus[libraryName] = true;
                return true;
            } catch (error) {
                console.warn(`CDN ${i + 1} 失敗，嘗試下一個...`);
                if (i === urls.length - 1) {
                    console.error(`❌ 所有 ${libraryName} CDN 都失敗了`);
                    return false;
                }
            }
        }
        return false;
    }
    
    // 檢查庫是否已載入
    function checkLibraryLoaded(libraryName) {
        switch (libraryName) {
            case 'react':
                return typeof window.React !== 'undefined';
            case 'reactDOM':
                return typeof window.ReactDOM !== 'undefined';
            case 'babel':
                return typeof window.Babel !== 'undefined';
            default:
                return false;
        }
    }
    
    // 主要載入函數
    async function loadDependencies() {
        console.log('🔄 開始載入依賴...');
        
        // 檢查是否已經載入
        for (const [lib, _] of Object.entries(CDN_SOURCES)) {
            if (checkLibraryLoaded(lib)) {
                console.log(`✅ ${lib} 已經載入`);
                loadStatus[lib] = true;
            }
        }
        
        // 載入 React
        if (!loadStatus.react) {
            console.log('📦 載入 React...');
            await loadLibraryWithFallback('react', CDN_SOURCES.react);
        }
        
        // 載入 ReactDOM
        if (!loadStatus.reactDOM) {
            console.log('📦 載入 ReactDOM...');
            await loadLibraryWithFallback('reactDOM', CDN_SOURCES.reactDOM);
        }
        
        // 載入 Babel
        if (!loadStatus.babel) {
            console.log('📦 載入 Babel...');
            await loadLibraryWithFallback('babel', CDN_SOURCES.babel);
        }
        
        // 檢查最終狀態
        const allLoaded = Object.values(loadStatus).every(status => status);
        
        if (allLoaded) {
            console.log('✅ 所有依賴載入成功！');
            
            // 觸發自定義事件
            window.dispatchEvent(new CustomEvent('dependenciesLoaded', {
                detail: { loadStatus }
            }));
            
            // 設定全域標記
            window._dependenciesLoaded = true;
            
        } else {
            console.error('❌ 部分依賴載入失敗:', loadStatus);
            
            // 顯示錯誤訊息
            showFallbackMessage();
        }
        
        return allLoaded;
    }
    
    // 顯示備用訊息
    function showFallbackMessage() {
        const message = document.createElement('div');
        message.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #fee2e2;
            border-bottom: 2px solid #fca5a5;
            color: #991b1b;
            padding: 15px;
            text-align: center;
            z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        
        message.innerHTML = `
            <strong>⚠️ 載入問題</strong><br>
            部分功能可能無法正常使用，請檢查網路連接或嘗試重新整理頁面。<br>
            <button onclick="location.reload()" style="margin-top: 10px; padding: 5px 15px; background: #dc2626; color: white; border: none; border-radius: 3px; cursor: pointer;">
                重新載入
            </button>
        `;
        
        document.body.insertBefore(message, document.body.firstChild);
        
        // 5秒後自動隱藏
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 5000);
    }
    
    // 導出到全域
    window.CDNFallback = {
        loadDependencies,
        loadStatus,
        checkLibraryLoaded
    };
    
    // 如果 DOM 已載入，立即開始載入
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', loadDependencies);
    } else {
        loadDependencies();
    }
    
})();
