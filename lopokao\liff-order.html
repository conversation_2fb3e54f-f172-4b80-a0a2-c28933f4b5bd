<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>融氏古早味蘿蔔糕 - LINE 訂購</title>
    
    <!-- LINE LIFF SDK -->
    <script charset="utf-8" src="https://static.line-scdn.net/liff/edge/2/sdk.js"></script>
    
    <!-- React 和 Babel -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- 自訂樣式 -->
    <style>
        /* LIFF 專用樣式 */
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background-color: #f8fafc;
            overflow-x: hidden;
        }
        
        .liff-container {
            max-width: 414px;
            min-height: 100vh;
            margin: 0 auto;
            background: white;
            position: relative;
        }
        
        /* 進度條樣式 */
        .progress-step {
            transition: all 0.3s ease;
        }
        
        .progress-step.active {
            background-color: #ef4444;
            color: white;
        }
        
        .progress-step.completed {
            background-color: #10b981;
            color: white;
        }
        
        /* 產品卡片樣式 */
        .product-card {
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            transition: all 0.2s ease;
        }
        
        .product-card.selected {
            border-color: #ef4444;
            background-color: #fef2f2;
        }
        
        /* 數量控制按鈕 */
        .quantity-btn {
            width: 44px;
            height: 44px;
            border-radius: 50%;
            border: none;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.2s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .quantity-btn:active {
            transform: scale(0.95);
        }
        
        /* 主要按鈕樣式 */
        .primary-btn {
            height: 50px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        
        .primary-btn:active {
            transform: scale(0.98);
        }
        
        /* 步驟容器 */
        .step-container {
            min-height: calc(100vh - 120px);
            padding: 20px;
        }
        
        /* 滾動區域 */
        .scroll-area {
            max-height: calc(100vh - 200px);
            overflow-y: auto;
        }
        
        /* 隱藏滾動條但保持滾動功能 */
        .scroll-area::-webkit-scrollbar {
            width: 3px;
        }
        
        .scroll-area::-webkit-scrollbar-track {
            background: #f1f5f9;
        }
        
        .scroll-area::-webkit-scrollbar-thumb {
            background: #cbd5e1;
            border-radius: 3px;
        }
        
        /* Loading 動畫 */
        .loading-spinner {
            border: 3px solid #f3f4f6;
            border-top: 3px solid #ef4444;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 震動效果 */
        .shake {
            animation: shake 0.5s ease-in-out;
        }
        
        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }
    </style>
</head>
<body>
    <div id="liff-app"></div>
    
    <!-- 直接內嵌縣市資料 - 避免載入問題 -->
    <script>
        // 直接定義縣市地區資料
        window.cityDistricts = {
            "臺北市": ["中正區", "大同區", "中山區", "松山區", "大安區", "萬華區", "信義區", "士林區", "北投區", "內湖區", "南港區", "文山區"],
            "新北市": ["板橋區", "三重區", "中和區", "永和區", "新莊區", "新店區", "樹林區", "鶯歌區", "三峽區", "淡水區", "汐止區", "瑞芳區", "土城區", "蘆洲區", "五股區", "泰山區", "林口區", "深坑區", "石碇區", "坪林區", "三芝區", "石門區", "八里區", "平溪區", "雙溪區", "貢寮區", "金山區", "萬里區", "烏來區"],
            "基隆市": ["仁愛區", "信義區", "中正區", "中山區", "安樂區", "暖暖區", "七堵區"],
            "桃園市": ["桃園區", "中壢區", "平鎮區", "八德區", "楊梅區", "蘆竹區", "大溪區", "龜山區", "大園區", "觀音區", "新屋區", "龍潭區", "復興區"],
            "新竹市": ["東區", "北區", "香山區"],
            "新竹縣": ["竹北市", "竹東鎮", "新埔鎮", "關西鎮", "湖口鄉", "新豐鄉", "峨眉鄉", "寶山鄉", "北埔鄉", "芎林鄉", "橫山鄉", "尖石鄉", "五峰鄉"],
            "苗栗縣": ["苗栗市", "頭份市", "竹南鎮", "後龍鎮", "通霄鎮", "苑裡鎮", "卓蘭鎮", "造橋鄉", "西湖鄉", "頭屋鄉", "公館鄉", "銅鑼鄉", "三義鄉", "大湖鄉", "獅潭鄉", "三灣鄉", "南庄鄉", "泰安鄉"],
            "臺中市": ["中區", "東區", "南區", "西區", "北區", "北屯區", "西屯區", "南屯區", "太平區", "大里區", "霧峰區", "烏日區", "豐原區", "后里區", "石岡區", "東勢區", "和平區", "新社區", "潭子區", "大雅區", "神岡區", "大肚區", "沙鹿區", "龍井區", "梧棲區", "清水區", "大甲區", "外埔區", "大安區"],
            "彰化縣": ["彰化市", "員林市", "和美鎮", "鹿港鎮", "溪湖鎮", "二林鎮", "田中鎮", "北斗鎮", "花壇鄉", "芬園鄉", "大村鄉", "永靖鄉", "伸港鄉", "線西鄉", "福興鄉", "秀水鄉", "埔心鄉", "埔鹽鄉", "大城鄉", "芳苑鄉", "竹塘鄉", "社頭鄉", "二水鄉", "田尾鄉", "埤頭鄉", "溪州鄉"],
            "南投縣": ["南投市", "埔里鎮", "草屯鎮", "竹山鎮", "集集鎮", "名間鄉", "鹿谷鄉", "中寮鄉", "魚池鄉", "國姓鄉", "水里鄉", "信義鄉", "仁愛鄉"],
            "雲林縣": ["斗六市", "斗南鎮", "虎尾鎮", "西螺鎮", "土庫鎮", "北港鎮", "古坑鄉", "大埤鄉", "莿桐鄉", "林內鄉", "二崙鄉", "崙背鄉", "麥寮鄉", "東勢鄉", "褒忠鄉", "台西鄉", "元長鄉", "四湖鄉", "口湖鄉", "水林鄉"],
            "嘉義市": ["東區", "西區"],
            "嘉義縣": ["太保市", "朴子市", "布袋鎮", "大林鎮", "民雄鄉", "溪口鄉", "新港鄉", "六腳鄉", "東石鄉", "義竹鄉", "鹿草鄉", "水上鄉", "中埔鄉", "竹崎鄉", "梅山鄉", "番路鄉", "大埔鄉", "阿里山鄉"],
            "臺南市": ["中西區", "東區", "南區", "北區", "安平區", "安南區", "永康區", "歸仁區", "新化區", "左鎮區", "玉井區", "楠西區", "南化區", "仁德區", "關廟區", "龍崎區", "官田區", "麻豆區", "佳里區", "西港區", "七股區", "將軍區", "學甲區", "北門區", "新營區", "後壁區", "白河區", "東山區", "六甲區", "下營區", "柳營區", "鹽水區", "善化區", "大內區", "山上區", "新市區", "安定區"],
            "高雄市": ["楠梓區", "左營區", "鼓山區", "三民區", "鹽埕區", "前金區", "新興區", "苓雅區", "前鎮區", "旗津區", "小港區", "鳳山區", "大寮區", "鳥松區", "林園區", "仁武區", "大樹區", "大社區", "岡山區", "路竹區", "橋頭區", "梓官區", "彌陀區", "永安區", "燕巢區", "田寮區", "阿蓮區", "茄萣區", "湖內區", "旗山區", "美濃區", "內門區", "杉林區", "甲仙區", "六龜區", "茂林區", "桃源區", "那瑪夏區"],
            "屏東縣": ["屏東市", "潮州鎮", "東港鎮", "恆春鎮", "萬丹鄉", "長治鄉", "麟洛鄉", "九如鄉", "里港鄉", "鹽埔鄉", "高樹鄉", "萬巒鄉", "內埔鄉", "竹田鄉", "新埤鄉", "枋寮鄉", "新園鄉", "崁頂鄉", "林邊鄉", "南州鄉", "佳冬鄉", "琉球鄉", "車城鄉", "滿州鄉", "枋山鄉", "三地門鄉", "霧臺鄉", "瑪家鄉", "泰武鄉", "來義鄉", "春日鄉", "獅子鄉", "牡丹鄉"],
            "宜蘭縣": ["宜蘭市", "羅東鎮", "蘇澳鎮", "頭城鎮", "礁溪鄉", "壯圍鄉", "員山鄉", "冬山鄉", "五結鄉", "三星鄉", "大同鄉", "南澳鄉"],
            "花蓮縣": ["花蓮市", "鳳林鎮", "玉里鎮", "新城鄉", "吉安鄉", "壽豐鄉", "光復鄉", "豐濱鄉", "瑞穗鄉", "富里鄉", "秀林鄉", "萬榮鄉", "卓溪鄉"],
            "臺東縣": ["臺東市", "成功鎮", "關山鎮", "長濱鄉", "池上鄉", "東河鄉", "鹿野鄉", "卑南鄉", "大武鄉", "綠島鄉", "太麻里鄉", "海端鄉", "延平鄉", "金峰鄉", "達仁鄉", "蘭嶼鄉"],
            "澎湖縣": ["馬公市", "湖西鄉", "白沙鄉", "西嶼鄉", "望安鄉", "七美鄉"],
            "金門縣": ["金城鎮", "金湖鎮", "金沙鎮", "金寧鄉", "烈嶼鄉", "烏坵鄉"],
            "連江縣": ["南竿鄉", "北竿鄉", "莒光鄉", "東引鄉"]
        };
        
        console.log('✅ 縣市資料已直接載入，包含', Object.keys(window.cityDistricts).length, '個縣市');
    </script>
    
    <!-- 載入其他工具函數 -->
    <script src="./utils/sheetsUtils.js"></script>
    <script src="./utils/errorUtils.js"></script>
    
    <!-- 載入組件檔案 -->
    <script type="text/babel" src="./components/liff/CustomAlertNew.js?v=2.0.1"></script>
    <script type="text/babel" src="./components/liff/ProgressIndicator.js?v=1.0.1"></script>
    <script type="text/babel" src="./components/liff/Step1ProductSelection.js?v=1.0.1"></script>
    <script type="text/babel" src="./components/liff/Step2CustomerInfo.js?v=1.0.3"></script>
    <script type="text/babel" src="./components/liff/Step3OrderConfirm.js?v=1.0.1"></script>
    
    <!-- LIFF 主應用程式 -->
    <script type="text/babel">
        const { useState, useEffect, useRef } = React;
        
        // LIFF 訂購應用程式主組件
        function LIFFOrderApp() {
            const [liffReady, setLiffReady] = useState(false);
            const [userProfile, setUserProfile] = useState(null);
            const [currentStep, setCurrentStep] = useState(1);
            const [isLoading, setIsLoading] = useState(false);
            const [error, setError] = useState(null);
            
            // 訂單資料狀態
            const [orderData, setOrderData] = useState({
                products: {
                    radish: 0,
                    taro: 0,
                    hongkong: 0
                },
                shipping: 0,
                totalAmount: 0,
                customerName: '',
                phone: '',
                deliveryMethod: '',
                district: '',
                area: '',
                address: '',
                preferredDate: '',
                preferredTime: '上午 (13點前)',
                contactMethod: '',
                socialAccount: '',
                paymentMethod: '',
                notes: '',
                storeName: '',
                storeAddress: '',
                storeId: ''
            });
            
            // 初始化 LIFF
            useEffect(() => {
                initializeLIFF();
            }, []);
            
            const initializeLIFF = async () => {
                try {
                    // 檢測是否在 LIFF 環境中
                    if (typeof liff === 'undefined') {
                        console.warn('非 LIFF 環境，使用測試模式');
                        setLiffReady(true);
                        setUserProfile({
                            displayName: '測試用戶',
                            userId: 'test-user-id'
                        });
                        return;
                    }
                    
                    // 初始化 LIFF（需要您提供實際的 LIFF ID）
                    await liff.init({
                        liffId: '2007619149-RYmOkjxJ' // 請替換為實際的 LIFF ID
                    });
                    
                    if (!liff.isLoggedIn()) {
                        liff.login();
                        return;
                    }
                    
                    // 獲取用戶資料
                    const profile = await liff.getProfile();
                    setUserProfile(profile);
                    
                    // 預填用戶姓名
                    setOrderData(prev => ({
                        ...prev,
                        customerName: profile.displayName || '',
                        contactMethod: 'line',
                        socialAccount: profile.displayName || ''
                    }));
                    
                    setLiffReady(true);
                    
                } catch (error) {
                    console.error('LIFF 初始化失敗:', error);
                    setError('LIFF 初始化失敗，請重新開啟');
                    // 仍然允許使用，但顯示警告
                    setLiffReady(true);
                }
            };
            
            // 計算總價
            useEffect(() => {
                calculateTotal();
            }, [orderData.products]);
            
            const calculateTotal = () => {
                const radishTotal = (parseInt(orderData.products.radish) || 0) * 250;
                const taroTotal = (parseInt(orderData.products.taro) || 0) * 350;
                const hongkongTotal = (parseInt(orderData.products.hongkong) || 0) * 350;
                const subtotal = radishTotal + taroTotal + hongkongTotal;
                
                let shipping = 0;
                if (subtotal > 0 && subtotal < 350) {
                    shipping = 100;
                }
                
                setOrderData(prev => ({
                    ...prev,
                    shipping,
                    totalAmount: subtotal + shipping
                }));
            };
            
            // 步驟導航
            const goToNextStep = () => {
                if (currentStep < 3) {
                    setCurrentStep(currentStep + 1);
                }
            };
            
            const goToPrevStep = () => {
                if (currentStep > 1) {
                    setCurrentStep(currentStep - 1);
                }
            };
            
            // 載入中畫面
            if (!liffReady) {
                return (
                    <div className="liff-container flex items-center justify-center min-h-screen">
                        <div className="text-center">
                            <div className="loading-spinner mx-auto mb-4"></div>
                            <p className="text-gray-600">載入中...</p>
                        </div>
                    </div>
                );
            }
            
            // 錯誤畫面
            if (error) {
                return (
                    <div className="liff-container flex items-center justify-center min-h-screen">
                        <div className="text-center p-6">
                            <div className="text-red-500 text-6xl mb-4">⚠️</div>
                            <h2 className="text-xl font-bold mb-2">發生錯誤</h2>
                            <p className="text-gray-600 mb-4">{error}</p>
                            <button 
                                onClick={() => window.location.reload()}
                                className="primary-btn bg-red-500 text-white px-6 py-2"
                            >
                                重新載入
                            </button>
                        </div>
                    </div>
                );
            }
            
            return (
                <div className="liff-container">
                    {/* 自定義警告彈窗 */}
                    <CustomAlert />
                    
                    {/* 進度指示器 */}
                    <ProgressIndicator currentStep={currentStep} />
                    
                    {/* 步驟內容 */}
                    <div className="step-container">
                        {currentStep === 1 && (
                            <Step1ProductSelection 
                                orderData={orderData}
                                setOrderData={setOrderData}
                                onNext={goToNextStep}
                            />
                        )}
                        
                        {currentStep === 2 && (
                            <Step2CustomerInfo 
                                orderData={orderData}
                                setOrderData={setOrderData}
                                userProfile={userProfile}
                                onNext={goToNextStep}
                                onPrev={goToPrevStep}
                            />
                        )}
                        
                        {currentStep === 3 && (
                            <Step3OrderConfirm 
                                orderData={orderData}
                                userProfile={userProfile}
                                onPrev={goToPrevStep}
                                isLoading={isLoading}
                                setIsLoading={setIsLoading}
                            />
                        )}
                    </div>
                </div>
            );
        }
        
        // 渲染應用程式
        ReactDOM.render(<LIFFOrderApp />, document.getElementById('liff-app'));
    </script>
    
</body>
</html>