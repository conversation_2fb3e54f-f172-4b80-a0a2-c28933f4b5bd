@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Fix Blank Page Issue
echo ========================================
echo.

echo Checking system status...
echo.

:: Check if key files exist
if not exist "components\AlertContextBrowser.js" (
    echo [ERROR] AlertContextBrowser.js not found
    echo [INFO] This file is critical for fixing blank page
    echo.
) else (
    echo [OK] AlertContextBrowser.js exists
)

if not exist "utils\alertUpgradeUtils.js" (
    echo [ERROR] alertUpgradeUtils.js not found
) else (
    echo [OK] alertUpgradeUtils.js exists
)

if not exist "app.js" (
    echo [ERROR] app.js not found
) else (
    echo [OK] app.js exists

    :: Check if app.js contains problematic import statements
    findstr /C:"import {" app.js >nul 2>&1
    if %errorlevel% equ 0 (
        echo [WARNING] app.js still contains ES6 import statements
    ) else (
        echo [OK] app.js import issues fixed
    )
)

echo.
echo Opening diagnostic and repair tools...

:: First open emergency fix tool
echo Opening emergency fix tool...
start http://localhost/lopokao/emergency-fix.html

:: Wait 2 seconds then open CDN test
timeout /t 2 /nobreak >nul
echo Opening CDN test...
start http://localhost/lopokao/test-cdn.html

:: Wait 2 seconds then open main page
timeout /t 2 /nobreak >nul
echo Opening main page...
start http://localhost/lopokao/index.html

echo.
echo [SUCCESS] Pages opened!
echo.
echo If pages are still blank, please check:
echo    1. Browser Developer Tools Console tab
echo    2. Confirm no JavaScript errors
echo    3. Confirm all files loaded correctly
echo.
echo Common solutions:
echo    - Clear browser cache (Ctrl+F5)
echo    - Check if XAMPP is running
echo    - Verify file paths are correct
echo.

pause
