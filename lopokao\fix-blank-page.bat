@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   融氏古早味 - 修復空白頁面問題
echo ========================================
echo.

echo 🔍 檢查問題狀態...
echo.

:: 檢查關鍵檔案是否存在
if not exist "components\AlertContextBrowser.js" (
    echo ❌ AlertContextBrowser.js 不存在
    echo 📝 這個檔案是修復空白頁面的關鍵檔案
    echo.
) else (
    echo ✅ AlertContextBrowser.js 存在
)

if not exist "utils\alertUpgradeUtils.js" (
    echo ❌ alertUpgradeUtils.js 不存在
) else (
    echo ✅ alertUpgradeUtils.js 存在
)

if not exist "app.js" (
    echo ❌ app.js 不存在
) else (
    echo ✅ app.js 存在
    
    :: 檢查 app.js 是否包含問題的 import 語句
    findstr /C:"import {" app.js >nul 2>&1
    if %errorlevel% equ 0 (
        echo ⚠️  app.js 仍包含 ES6 import 語句，這會導致錯誤
    ) else (
        echo ✅ app.js 已修正 import 問題
    )
)

echo.
echo 🚀 正在開啟診斷和修復工具...

:: 首先開啟緊急修復工具
echo 🚨 開啟緊急修復工具...
start http://localhost/lopokao/emergency-fix.html

:: 等待 2 秒後開啟 CDN 測試
timeout /t 2 /nobreak >nul
echo 📡 開啟 CDN 測試...
start http://localhost/lopokao/test-cdn.html

:: 等待 2 秒後開啟主頁面
timeout /t 2 /nobreak >nul
echo 🏠 開啟主頁面...
start http://localhost/lopokao/index.html

echo.
echo ✅ 頁面已開啟！
echo.
echo 🔧 如果頁面仍然空白，請檢查：
echo    1. 瀏覽器開發者工具的 Console 標籤
echo    2. 確認沒有 JavaScript 錯誤
echo    3. 確認所有檔案都已正確載入
echo.
echo 📋 常見問題解決方案：
echo    • 清除瀏覽器快取 (Ctrl+F5)
echo    • 檢查 XAMPP 是否正在運行
echo    • 確認檔案路徑正確
echo.

pause
