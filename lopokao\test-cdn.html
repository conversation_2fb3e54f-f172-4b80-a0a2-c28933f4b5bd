<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CDN 連結測試</title>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }
        .status {
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 4px solid #ddd;
        }
        .status-ok {
            background: #f0f9ff;
            border-left-color: #10b981;
        }
        .status-error {
            background: #fef2f2;
            border-left-color: #ef4444;
        }
        .button {
            background: #8b4513;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background: #6d3611;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔗 CDN 連結測試</h1>
        <p>測試各種 CDN 連結的載入狀態</p>
        
        <div id="test-results">
            <div class="status">正在測試...</div>
        </div>
        
        <div>
            <button class="button" onclick="testCDNs()">重新測試</button>
            <button class="button" onclick="testReact()">測試 React</button>
            <button class="button" onclick="loadAlternativeCDN()">載入備用 CDN</button>
        </div>
        
        <h2>📋 測試結果詳情</h2>
        <div id="detailed-results"></div>
    </div>

    <!-- 測試不同的 CDN 來源 -->
    <script>
        let testResults = [];
        
        function addResult(name, status, message) {
            testResults.push({ name, status, message, timestamp: new Date() });
            updateDisplay();
        }
        
        function updateDisplay() {
            const container = document.getElementById('test-results');
            const detailed = document.getElementById('detailed-results');
            
            container.innerHTML = testResults.map(result => {
                const statusClass = result.status ? 'status-ok' : 'status-error';
                const icon = result.status ? '✅' : '❌';
                return `
                    <div class="${statusClass} status">
                        ${icon} <strong>${result.name}</strong>: ${result.message}
                    </div>
                `;
            }).join('');
            
            detailed.innerHTML = `
                <pre>${JSON.stringify(testResults, null, 2)}</pre>
            `;
        }
        
        function testCDNs() {
            testResults = [];
            addResult('測試開始', true, '正在檢查各種 CDN 連結...');
            
            // 測試當前載入的庫
            setTimeout(() => {
                addResult('React', typeof React !== 'undefined', 
                    typeof React !== 'undefined' ? `版本: ${React.version || '未知'}` : '未載入');
                
                addResult('ReactDOM', typeof ReactDOM !== 'undefined', 
                    typeof ReactDOM !== 'undefined' ? '已載入' : '未載入');
                
                addResult('Babel', typeof Babel !== 'undefined', 
                    typeof Babel !== 'undefined' ? '已載入' : '未載入');
                
                // 測試網路連接
                testNetworkConnection();
            }, 100);
        }
        
        function testNetworkConnection() {
            fetch('https://unpkg.com/react@18/package.json')
                .then(response => response.json())
                .then(data => {
                    addResult('網路連接', true, `可以訪問 unpkg.com，React 版本: ${data.version}`);
                })
                .catch(error => {
                    addResult('網路連接', false, `無法訪問 unpkg.com: ${error.message}`);
                    // 嘗試其他 CDN
                    testAlternativeCDNs();
                });
        }
        
        function testAlternativeCDNs() {
            const alternatives = [
                'https://cdn.jsdelivr.net/npm/react@18/package.json',
                'https://cdnjs.cloudflare.com/ajax/libs/react/18.2.0/package.json'
            ];
            
            alternatives.forEach((url, index) => {
                fetch(url)
                    .then(response => response.json())
                    .then(data => {
                        addResult(`備用 CDN ${index + 1}`, true, `可用: ${url}`);
                    })
                    .catch(error => {
                        addResult(`備用 CDN ${index + 1}`, false, `不可用: ${url}`);
                    });
            });
        }
        
        function testReact() {
            if (typeof React !== 'undefined') {
                try {
                    const element = React.createElement('div', null, 'React 測試成功！');
                    addResult('React 功能測試', true, 'React.createElement 正常工作');
                    
                    if (typeof ReactDOM !== 'undefined') {
                        const container = document.createElement('div');
                        ReactDOM.render(element, container);
                        addResult('ReactDOM 功能測試', true, 'ReactDOM.render 正常工作');
                    }
                } catch (error) {
                    addResult('React 功能測試', false, `錯誤: ${error.message}`);
                }
            } else {
                addResult('React 功能測試', false, 'React 未載入');
            }
        }
        
        function loadAlternativeCDN() {
            addResult('載入備用 CDN', true, '正在嘗試載入備用 CDN...');
            
            // 載入 jsDelivr CDN
            const scripts = [
                'https://cdn.jsdelivr.net/npm/react@18/umd/react.development.js',
                'https://cdn.jsdelivr.net/npm/react-dom@18/umd/react-dom.development.js',
                'https://cdn.jsdelivr.net/npm/@babel/standalone/babel.min.js'
            ];
            
            let loadedCount = 0;
            scripts.forEach((src, index) => {
                const script = document.createElement('script');
                script.src = src;
                script.onload = () => {
                    loadedCount++;
                    addResult(`備用腳本 ${index + 1}`, true, `已載入: ${src}`);
                    if (loadedCount === scripts.length) {
                        setTimeout(testCDNs, 500);
                    }
                };
                script.onerror = () => {
                    addResult(`備用腳本 ${index + 1}`, false, `載入失敗: ${src}`);
                };
                document.head.appendChild(script);
            });
        }
        
        // 頁面載入時自動測試
        window.addEventListener('load', () => {
            setTimeout(testCDNs, 1000);
        });
    </script>
    
    <!-- 主要 CDN 連結 -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
</body>
</html>
