/**
 * 瀏覽器兼容版本的 AlertContext
 * 使用全域變數而非 ES6 模組
 */

// 使用 React 的解構
const { createContext, useContext, useState, useCallback, useEffect, createElement } = React;

// 創建 Context
const AlertContext = createContext();

// 簡化版的 CustomAlert 組件（瀏覽器兼容）
function BrowserCustomAlert({ isVisible, title, message, type = 'info', onClose }) {
    if (!isVisible) return null;
    
    const getTheme = (type) => {
        switch (type) {
            case 'success':
                return {
                    bgColor: 'bg-green-50',
                    borderColor: 'border-green-200',
                    iconBg: 'bg-green-100',
                    iconColor: 'text-green-600',
                    titleColor: 'text-green-900',
                    buttonColor: 'bg-green-600 hover:bg-green-700'
                };
            case 'error':
                return {
                    bgColor: 'bg-red-50',
                    borderColor: 'border-red-200',
                    iconBg: 'bg-red-100',
                    iconColor: 'text-red-600',
                    titleColor: 'text-red-900',
                    buttonColor: 'bg-red-600 hover:bg-red-700'
                };
            case 'warning':
                return {
                    bgColor: 'bg-yellow-50',
                    borderColor: 'border-yellow-200',
                    iconBg: 'bg-yellow-100',
                    iconColor: 'text-yellow-600',
                    titleColor: 'text-yellow-900',
                    buttonColor: 'bg-yellow-600 hover:bg-yellow-700'
                };
            default:
                return {
                    bgColor: 'bg-blue-50',
                    borderColor: 'border-blue-200',
                    iconBg: 'bg-blue-100',
                    iconColor: 'text-blue-600',
                    titleColor: 'text-blue-900',
                    buttonColor: 'bg-blue-600 hover:bg-blue-700'
                };
        }
    };
    
    const theme = getTheme(type);
    
    // 圖示組件
    const getIcon = (type) => {
        switch (type) {
            case 'success':
                return createElement('svg', {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24"
                }, createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                }));
            case 'error':
                return createElement('svg', {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24"
                }, createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
                }));
            case 'warning':
                return createElement('svg', {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24"
                }, createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                }));
            default:
                return createElement('svg', {
                    className: "w-6 h-6",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24"
                }, createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                }));
        }
    };

    return createElement('div', {
        className: "fixed inset-0 z-50 flex items-center justify-center"
    }, [
        // 背景遮罩
        createElement('div', {
            key: 'backdrop',
            className: `absolute inset-0 bg-black transition-opacity duration-300 ${isVisible ? 'opacity-60' : 'opacity-0'}`,
            onClick: onClose
        }),
        
        // 彈窗主體
        createElement('div', {
            key: 'modal',
            className: `relative z-10 w-full max-w-md mx-4 bg-white rounded-2xl shadow-2xl overflow-hidden transform transition-all duration-300 ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-95'}`
        }, [
            // 標題和圖示
            createElement('div', {
                key: 'header',
                className: `flex items-center p-5 ${theme.bgColor} border-b ${theme.borderColor}`
            }, [
                createElement('div', {
                    key: 'icon',
                    className: `flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-full ${theme.iconBg} ${theme.iconColor}`
                }, getIcon(type)),
                
                createElement('div', {
                    key: 'title-container',
                    className: "ml-4 flex-1"
                }, createElement('h3', {
                    className: `text-lg font-semibold ${theme.titleColor}`
                }, title)),
                
                createElement('button', {
                    key: 'close-btn',
                    onClick: onClose,
                    className: "flex-shrink-0 text-gray-400 hover:text-gray-600 transition-colors duration-200 p-1 rounded-full hover:bg-gray-100",
                    'aria-label': "關閉"
                }, createElement('svg', {
                    className: "w-5 h-5",
                    fill: "none",
                    stroke: "currentColor",
                    viewBox: "0 0 24 24"
                }, createElement('path', {
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    strokeWidth: 2,
                    d: "M6 18L18 6M6 6l12 12"
                })))
            ]),
            
            // 內容
            createElement('div', {
                key: 'content',
                className: "p-6"
            }, createElement('p', {
                className: "text-base text-gray-700 leading-relaxed whitespace-pre-wrap break-words"
            }, message)),
            
            // 按鈕
            createElement('div', {
                key: 'footer',
                className: "px-6 py-4 bg-gray-50 flex justify-end"
            }, createElement('button', {
                onClick: onClose,
                className: `px-8 py-2.5 text-white font-semibold rounded-lg shadow-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-all duration-200 ${theme.buttonColor}`
            }, '確定'))
        ])
    ]);
}

// AlertProvider 組件
function AlertProvider({ children }) {
    const [alertState, setAlertState] = useState({
        isVisible: false,
        title: '',
        message: '',
        type: 'info',
        onConfirm: null,
    });

    const showAlert = useCallback((message, options = {}) => {
        setAlertState({
            isVisible: true,
            message,
            title: options.title || '融氏蘿蔔糕',
            type: options.type || 'info',
            onConfirm: options.onConfirm || null,
        });
    }, []);

    const hideAlert = useCallback(() => {
        if (alertState.onConfirm) {
            alertState.onConfirm();
        }
        setAlertState((prev) => ({ ...prev, isVisible: false }));
    }, [alertState]);

    // 提供便利的快速方法
    const showSuccess = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'success', title, onConfirm }), [showAlert]);
    const showError = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'error', title, onConfirm }), [showAlert]);
    const showWarning = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'warning', title, onConfirm }), [showAlert]);
    const showInfo = useCallback((message, title = '融氏蘿蔔糕', onConfirm) =>
        showAlert(message, { type: 'info', title, onConfirm }), [showAlert]);

    // 向後兼容：註冊全域函數
    useEffect(() => {
        window.customAlert = showAlert;
        window.showSuccess = showSuccess;
        window.showError = showError;
        window.showWarning = showWarning;
        window.showInfo = showInfo;
        window._alertSystemUpgraded = true;
        window._alertVersion = '2.0';

        return () => {
            delete window.customAlert;
            delete window.showSuccess;
            delete window.showError;
            delete window.showWarning;
            delete window.showInfo;
            delete window._alertSystemUpgraded;
            delete window._alertVersion;
        };
    }, [showAlert, showSuccess, showError, showWarning, showInfo]);

    const contextValue = {
        showAlert,
        showSuccess,
        showError,
        showWarning,
        showInfo,
    };

    return createElement(AlertContext.Provider, { value: contextValue }, [
        children,
        createElement(BrowserCustomAlert, {
            key: 'alert',
            isVisible: alertState.isVisible,
            title: alertState.title,
            message: alertState.message,
            type: alertState.type,
            onClose: hideAlert
        })
    ]);
}

// useAlert Hook
function useAlert() {
    const context = useContext(AlertContext);
    if (!context) {
        throw new Error('useAlert 必須在 AlertProvider 內部使用');
    }
    return context;
}

// 全域導出
window.AlertProvider = AlertProvider;
window.useAlert = useAlert;
window.AlertContext = AlertContext;
