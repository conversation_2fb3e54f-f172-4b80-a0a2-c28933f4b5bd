@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   融氏古早味 - 建置生產環境 CSS
echo ========================================
echo.

echo 🔄 正在建置 Tailwind CSS...

:: 檢查是否已安裝 Tailwind CLI
where tailwindcss >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Tailwind CSS CLI 未安裝
    echo 📦 正在安裝 Tailwind CSS CLI...
    npm install -D tailwindcss
    if %errorlevel% neq 0 (
        echo ❌ 安裝失敗，請手動執行: npm install -D tailwindcss
        pause
        exit /b 1
    )
)

:: 建置 CSS
echo 🏗️ 建置中...
npx tailwindcss -i ./styles/tailwind.css -o ./dist/styles.css --watch

echo.
echo ✅ CSS 建置完成！
echo 📁 輸出檔案: ./dist/styles.css
echo.
echo 💡 使用說明：
echo    1. 在 HTML 中將 CDN 連結替換為本地檔案
echo    2. 使用 --watch 參數可以監控檔案變化自動重建
echo.

pause
