@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   融氏古早味 Alert 系統升級測試
echo ========================================
echo.

echo 🔄 正在啟動測試頁面...
echo.

echo 📋 可用的測試頁面：
echo.
echo 1. 基本功能測試
echo    http://localhost/lopokao/test-new-alert.html
echo.
echo 2. 漸進式升級測試 (推薦)
echo    http://localhost/lopokao/test-progressive-upgrade.html
echo.
echo 3. 主應用測試
echo    http://localhost/lopokao/index.html
echo.
echo 4. LIFF 頁面測試
echo    http://localhost/lopokao/liff-order.html
echo.

echo 🚀 正在開啟漸進式升級測試頁面...
start http://localhost/lopokao/test-progressive-upgrade.html

echo.
echo ✅ 測試頁面已開啟！
echo.
echo 💡 使用說明：
echo    • 在測試頁面中點擊各種按鈕測試功能
echo    • 開啟瀏覽器開發者工具查看詳細資訊
echo    • 執行 AlertUpgradeUtils.showUpgradeReport() 查看升級狀態
echo.
echo 📖 詳細文件：CUSTOM_ALERT_UPGRADE_GUIDE.md
echo.

pause
