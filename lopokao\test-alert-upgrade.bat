@echo off
chcp 65001 >nul
echo.
echo ========================================
echo   Alert System Upgrade Test
echo ========================================
echo.

echo Starting test pages...
echo.

echo Available test pages:
echo.
echo 1. Basic functionality test
echo    http://localhost/lopokao/test-new-alert.html
echo.
echo 2. Progressive upgrade test (Recommended)
echo    http://localhost/lopokao/test-progressive-upgrade.html
echo.
echo 3. Main application test
echo    http://localhost/lopokao/index.html
echo.
echo 4. LIFF page test
echo    http://localhost/lopokao/liff-order.html
echo.

echo Opening progressive upgrade test page...
start http://localhost/lopokao/test-progressive-upgrade.html

echo.
echo [SUCCESS] Test page opened!
echo.
echo Usage instructions:
echo    - Click various buttons on test page to test functionality
echo    - Open browser developer tools for detailed information
echo    - Run AlertUpgradeUtils.showUpgradeReport() to check upgrade status
echo.
echo Documentation: CUSTOM_ALERT_UPGRADE_GUIDE.md
echo.

pause
