{"version": 3, "file": "index.js", "sources": ["../../src/index.tsx"], "sourcesContent": ["import * as React from 'react'\nimport { flushSync } from 'react-dom'\nimport {\n  Virtualizer,\n  elementScroll,\n  observeElementOffset,\n  observeElementRect,\n  observeWindowOffset,\n  observeWindowRect,\n  windowScroll,\n} from '@tanstack/virtual-core'\nimport type { PartialKeys, VirtualizerOptions } from '@tanstack/virtual-core'\n\nexport * from '@tanstack/virtual-core'\n\nconst useIsomorphicLayoutEffect =\n  typeof document !== 'undefined' ? React.useLayoutEffect : React.useEffect\n\nfunction useVirtualizerBase<\n  TScrollElement extends Element | Window,\n  TItemElement extends Element,\n>(\n  options: VirtualizerOptions<TScrollElement, TItemElement>,\n): Virtualizer<TScrollElement, TItemElement> {\n  const rerender = React.useReducer(() => ({}), {})[1]\n\n  const resolvedOptions: VirtualizerOptions<TScrollElement, TItemElement> = {\n    ...options,\n    onChange: (instance, sync) => {\n      if (sync) {\n        flushSync(rerender)\n      } else {\n        rerender()\n      }\n      options.onChange?.(instance, sync)\n    },\n  }\n\n  const [instance] = React.useState(\n    () => new Virtualizer<TScrollElement, TItemElement>(resolvedOptions),\n  )\n\n  instance.setOptions(resolvedOptions)\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._didMount()\n  }, [])\n\n  useIsomorphicLayoutEffect(() => {\n    return instance._willUpdate()\n  })\n\n  return instance\n}\n\nexport function useVirtualizer<\n  TScrollElement extends Element,\n  TItemElement extends Element,\n>(\n  options: PartialKeys<\n    VirtualizerOptions<TScrollElement, TItemElement>,\n    'observeElementRect' | 'observeElementOffset' | 'scrollToFn'\n  >,\n): Virtualizer<TScrollElement, TItemElement> {\n  return useVirtualizerBase<TScrollElement, TItemElement>({\n    observeElementRect: observeElementRect,\n    observeElementOffset: observeElementOffset,\n    scrollToFn: elementScroll,\n    ...options,\n  })\n}\n\nexport function useWindowVirtualizer<TItemElement extends Element>(\n  options: PartialKeys<\n    VirtualizerOptions<Window, TItemElement>,\n    | 'getScrollElement'\n    | 'observeElementRect'\n    | 'observeElementOffset'\n    | 'scrollToFn'\n  >,\n): Virtualizer<Window, TItemElement> {\n  return useVirtualizerBase<Window, TItemElement>({\n    getScrollElement: () => (typeof document !== 'undefined' ? window : null),\n    observeElementRect: observeWindowRect,\n    observeElementOffset: observeWindowOffset,\n    scrollToFn: windowScroll,\n    initialOffset: () => (typeof document !== 'undefined' ? window.scrollY : 0),\n    ...options,\n  })\n}\n"], "names": ["instance"], "mappings": ";;;;AAeA,MAAM,4BACJ,OAAO,aAAa,cAAc,MAAM,kBAAkB,MAAM;AAElE,SAAS,mBAIP,SAC2C;AACrC,QAAA,WAAW,MAAM,WAAW,OAAO,CAAA,IAAK,CAAA,CAAE,EAAE,CAAC;AAEnD,QAAM,kBAAoE;AAAA,IACxE,GAAG;AAAA,IACH,UAAU,CAACA,WAAU,SAAS;;AAC5B,UAAI,MAAM;AACR,kBAAU,QAAQ;AAAA,MAAA,OACb;AACI,iBAAA;AAAA,MAAA;AAEH,oBAAA,aAAA,iCAAWA,WAAU;AAAA,IAAI;AAAA,EAErC;AAEM,QAAA,CAAC,QAAQ,IAAI,MAAM;AAAA,IACvB,MAAM,IAAI,YAA0C,eAAe;AAAA,EACrE;AAEA,WAAS,WAAW,eAAe;AAEnC,4BAA0B,MAAM;AAC9B,WAAO,SAAS,UAAU;AAAA,EAC5B,GAAG,EAAE;AAEL,4BAA0B,MAAM;AAC9B,WAAO,SAAS,YAAY;AAAA,EAAA,CAC7B;AAEM,SAAA;AACT;AAEO,SAAS,eAId,SAI2C;AAC3C,SAAO,mBAAiD;AAAA,IACtD;AAAA,IACA;AAAA,IACA,YAAY;AAAA,IACZ,GAAG;AAAA,EAAA,CACJ;AACH;AAEO,SAAS,qBACd,SAOmC;AACnC,SAAO,mBAAyC;AAAA,IAC9C,kBAAkB,MAAO,OAAO,aAAa,cAAc,SAAS;AAAA,IACpE,oBAAoB;AAAA,IACpB,sBAAsB;AAAA,IACtB,YAAY;AAAA,IACZ,eAAe,MAAO,OAAO,aAAa,cAAc,OAAO,UAAU;AAAA,IACzE,GAAG;AAAA,EAAA,CACJ;AACH;"}