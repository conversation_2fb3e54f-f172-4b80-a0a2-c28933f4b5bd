<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>測試新版 CustomAlert</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            background-color: #f7e4d4;
            padding: 20px;
        }
        .test-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .test-button {
            background-color: #8b4513;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: background-color 0.3s;
        }
        .test-button:hover {
            background-color: #6d3611;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 style="color: #8b4513; text-align: center; margin-bottom: 30px;">
            新版 CustomAlert 測試
        </h1>
        
        <div style="text-align: center;">
            <button class="test-button" onclick="testSuccess()">測試成功訊息</button>
            <button class="test-button" onclick="testError()">測試錯誤訊息</button>
            <button class="test-button" onclick="testWarning()">測試警告訊息</button>
            <button class="test-button" onclick="testInfo()">測試資訊訊息</button>
            <button class="test-button" onclick="testLongMessage()">測試長訊息</button>
        </div>
        
        <div id="alert-container"></div>
    </div>

    <!-- 載入新版 CustomAlert -->
    <script type="text/babel" src="./components/liff/CustomAlertNew.js"></script>
    
    <script type="text/babel">
        const { useState } = React;
        
        function TestApp() {
            return (
                <div>
                    <CustomAlert />
                </div>
            );
        }
        
        // 渲染應用程式
        ReactDOM.render(<TestApp />, document.getElementById('alert-container'));
        
        // 測試函數
        function testSuccess() {
            window.showSuccess('訂單提交成功！\n\n您的訂單已經成功建立，我們會盡快為您處理。', '融氏古早味');
        }
        
        function testError() {
            window.showError('訂單提交失敗！\n\n請檢查您的網路連線並重試。', '系統錯誤');
        }
        
        function testWarning() {
            window.showWarning('請注意配送時間！\n\n由於假日關係，配送時間可能會延遲。', '配送提醒');
        }
        
        function testInfo() {
            window.showInfo('歡迎使用融氏古早味訂購系統！\n\n請選擇您要的商品並填寫相關資訊。', '系統資訊');
        }
        
        function testLongMessage() {
            const longMessage = `這是一個很長的測試訊息，用來測試彈窗是否能正確處理長文字內容。

訂單詳情：
• 商品：古早味蘿蔔糕 x 2
• 價格：NT$ 200
• 配送方式：宅配
• 配送地址：台北市信義區信義路五段7號

注意事項：
1. 請確保收件地址正確
2. 配送時間為工作日 9:00-18:00
3. 如有問題請聯繫客服

感謝您的訂購！`;
            
            window.showInfo(longMessage, '訂單確認');
        }
    </script>
</body>
</html>
