<?php
/**
 * 訂單查詢系統
 * 允許客戶通過電話號碼查詢訂單狀態
 */

require __DIR__ . '/../vendor/autoload.php';

use Google\Client;
use Google\Service\Sheets;
use Google\Service\Sheets\ValueRange;

class OrderQuerySystem {
    private $client;
    private $service;
    private $spreadsheetId = '10MMALrfBonchPGjb-ps6Knw7MV6lllrrKRCTeafCIuo';
    private $orderSheetName = 'Sheet1';
    
    public function __construct() {
        $this->initializeClient();
    }
    
    /**
     * 初始化Google API客戶端
     */
    private function initializeClient() {
        $this->client = new Client();
        $this->client->setApplicationName('訂單查詢系統');
        $this->client->setScopes([Sheets::SPREADSHEETS]);
        $this->client->setAuthConfig(__DIR__ . '/service-account-key2.json');
        $this->client->setAccessType('offline');

        // 設定 HTTP 客戶端選項以解決 SSL 問題
        $httpClient = new \GuzzleHttp\Client([
            'verify' => false, // 在開發環境中暫時停用 SSL 驗證
            'timeout' => 30,
            'connect_timeout' => 10
        ]);
        $this->client->setHttpClient($httpClient);

        $this->service = new Sheets($this->client);
    }
    
    /**
     * 查詢訂單
     * @param string $searchType 查詢類型 (僅支援 phone)
     * @param string $searchValue 查詢值
     * @return array 查詢結果
     */
    public function queryOrders($searchType, $searchValue) {
        try {
            if (empty($searchType) || empty($searchValue)) {
                return [
                    'success' => false,
                    'message' => '請輸入查詢條件',
                    'orders' => []
                ];
            }
            
            // 獲取訂單數據
            $range = $this->orderSheetName;
            $response = $this->service->spreadsheets_values->get($this->spreadsheetId, $range);
            $values = $response->getValues();
            
            if (empty($values)) {
                return [
                    'success' => false,
                    'message' => '沒有找到訂單數據',
                    'orders' => []
                ];
            }
            
            // 獲取標題行
            $headers = array_shift($values);
            
            // 查找對應的列索引
            $timestampIndex = array_search('訂單時間', $headers);
            if ($timestampIndex === false) $timestampIndex = 0; // 默認第一列是訂單時間

            $nameIndex = array_search('姓名', $headers);
            if ($nameIndex === false) $nameIndex = 1; // 默認第二列是姓名
            
            $phoneIndex = array_search('電話', $headers);
            if ($phoneIndex === false) $phoneIndex = 2; // 默認第三列是電話
            
            $orderItemsIndex = array_search('訂單項目', $headers);
            if ($orderItemsIndex === false) $orderItemsIndex = 8; // 默認第九列是訂單項目
            
            $totalAmountIndex = array_search('總金額', $headers);
            if ($totalAmountIndex === false) $totalAmountIndex = 9; // 默認第十列是總金額
            
            $deliveryDateIndex = array_search('希望到貨日', $headers);
            if ($deliveryDateIndex === false) $deliveryDateIndex = 5; // 默認第六列是希望到貨日
            
            $deliveryMethodIndex = array_search('配送方式', $headers);
            if ($deliveryMethodIndex === false) $deliveryMethodIndex = 3; // 默認第四列是配送方式
            
            // 過濾訂單
            $matchedOrders = [];
            foreach ($values as $row) {
                $rowtimestamp = isset($row[$timestampIndex]) ? $row[$timestampIndex] : '';
                $rowName = isset($row[$nameIndex]) ? $row[$nameIndex] : '';
                $rowPhone = isset($row[$phoneIndex]) ? $row[$phoneIndex] : '';
                
                // 移除電話號碼中可能的單引號前綴
                $rowPhone = ltrim($rowPhone, "'");
                
                // 僅使用電話號碼進行匹配
                $isMatch = false;
                // 移除搜索值中的非數字字符
                $searchPhone = preg_replace('/\D/', '', $searchValue);
                $rowPhoneDigits = preg_replace('/\D/', '', $rowPhone);
                
                if (stripos($rowPhoneDigits, $searchPhone) !== false) {
                    $isMatch = true;
                }
                
                if ($isMatch) {
                    // 處理日期格式，計算與當前日期的差距
                    $deliveryDate = isset($row[$deliveryDateIndex]) ? $row[$deliveryDateIndex] : '';
                    $orderDateStr = $rowtimestamp; // 使用訂單時間
                    $daysDiff = null;
                    $isRecent = false;
                    $orderTimestamp = PHP_INT_MAX; // 默認值，用於排序
                    
                    // 嘗試解析訂單時間 (假設格式為 m/d/Y H:i:s 或 Y-m-d H:i:s)
                    if (!empty($orderDateStr)) {
                        // 嘗試多種格式解析
                        $parsedTimestamp = strtotime(str_replace('/', '-', $orderDateStr));
                        if ($parsedTimestamp === false) {
                            // 嘗試另一種常見格式，例如 Google Sheets 預設
                            $dateTime = DateTime::createFromFormat('m/d/Y H:i:s', $orderDateStr);
                            if ($dateTime) {
                                $parsedTimestamp = $dateTime->getTimestamp();
                            }
                        }
                        
                        if ($parsedTimestamp !== false) {
                            $orderTimestamp = $parsedTimestamp;
                            $currentTimestamp = time();
                            $daysDiff = floor(($currentTimestamp - $orderTimestamp) / (60 * 60 * 24));
                            $isRecent = ($daysDiff <= 15); // 15天內的訂單視為最新訂單
                        }
                    }
                    
                    $matchedOrders[] = [
                        'orderTimestampValue' => $rowtimestamp, // 保留原始訂單時間字串
                        'name' => $rowName,
                        'phone' => $rowPhone,
                        'orderItems' => isset($row[$orderItemsIndex]) ? $row[$orderItemsIndex] : '',
                        'totalAmount' => isset($row[$totalAmountIndex]) ? $row[$totalAmountIndex] : '',
                        'deliveryDate' => $deliveryDate,
                        'deliveryMethod' => isset($row[$deliveryMethodIndex]) ? $row[$deliveryMethodIndex] : '',
                        'daysDiff' => $daysDiff, // 內部使用
                        'isRecent' => $isRecent, // 內部使用
                        'timestampForSort' => $orderTimestamp // 用於排序的時間戳
                    ];
                }
            }
            
            // 按訂單時間排序，最近的日期優先
            usort($matchedOrders, function($a, $b) {
                // 使用 'timestampForSort' 進行排序
                return $b['timestampForSort'] <=> $a['timestampForSort']; // 降序排列，最近的日期在前
            });

            // Determine if there are recent/old orders before creating final list
            $hasRecent = false;
            $hasOld = false;
            foreach ($matchedOrders as $order) {
                if ($order['isRecent']) {
                    $hasRecent = true;
                } else {
                    $hasOld = true;
                }
                if ($hasRecent && $hasOld) break; // Optimization
            }

            // Prepare final data for frontend, including the isRecent flag
            $finalOrders = [];
            foreach ($matchedOrders as $order) {
                $finalOrders[] = [
                    'timestamp' => $order['orderTimestampValue'], // Use original string
                    'name' => $order['name'],
                    'phone' => $order['phone'],
                    'orderItems' => $order['orderItems'],
                    'totalAmount' => $order['totalAmount'],
                    'deliveryDate' => $order['deliveryDate'],
                    'deliveryMethod' => $order['deliveryMethod'],
                    'isRecent' => $order['isRecent'] // Pass the flag to frontend
                ];
            }

            // Return the sorted list with flags
            return [
                'success' => true,
                'message' => count($finalOrders) > 0 ? '找到 ' . count($finalOrders) . ' 筆訂單' : '沒有找到符合條件的訂單',
                'orders' => $finalOrders, // Already sorted, includes isRecent flag
                'hasRecentOrders' => $hasRecent, // Use the calculated flag
                'hasOldOrders' => $hasOld       // Use the calculated flag
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'message' => '查詢時發生錯誤: ' . $e->getMessage(),
                'orders' => []
            ];
        }
    }
}

// 處理AJAX請求
$isAjax = !empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && $isAjax) {
    // 關閉錯誤顯示，避免錯誤訊息混入 JSON 輸出
    ini_set('display_errors', 0);
    error_reporting(E_ALL);

    // 開始輸出緩衝
    ob_start();

    try {
        $querySystem = new OrderQuerySystem();
        $searchType = 'phone'; // 固定使用電話號碼查詢
        $searchValue = isset($_POST['searchValue']) ? $_POST['searchValue'] : '';

        $result = $querySystem->queryOrders($searchType, $searchValue);

        // 清理輸出緩衝區，確保沒有其他輸出
        ob_clean();

        // 設定正確的 header
        header('Content-Type: application/json; charset=utf-8');
        header('Cache-Control: no-cache, must-revalidate');

        // 輸出 JSON
        echo json_encode($result, JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        // 清理輸出緩衝區
        ob_clean();

        // 記錄錯誤到日誌檔案
        error_log("Order Query Error: " . $e->getMessage() . " in " . $e->getFile() . " on line " . $e->getLine());

        // 回傳錯誤 JSON
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode([
            'success' => false,
            'message' => '系統發生錯誤，請稍後再試',
            'orders' => [],
            'debug' => [
                'error' => $e->getMessage(),
                'file' => basename($e->getFile()),
                'line' => $e->getLine()
            ]
        ], JSON_UNESCAPED_UNICODE);
    }

    // 結束輸出緩衝並清理
    ob_end_flush();
    exit;
}
?>

<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>融氏古早味 - 訂單查詢</title>
    <style>
        body {
            font-family: "微軟正黑體", "Microsoft JhengHei", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f7e4d4;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #8b4513;
            font-size: 28px;
            margin-bottom: 10px;
        }
        
        .search-form {
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #8b4513;
        }
        
        input[type="text"] {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
            box-sizing: border-box;
        }
        
        .radio-group {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .radio-option {
            display: flex;
            align-items: center;
        }
        
        .radio-option input {
            margin-right: 5px;
        }
        
        button {
            background-color: #8b4513;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            width: 100%;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        
        button:hover {
            background-color: #6d3611;
        }
        
        .results {
            display: none;
            background-color: #fff;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }
        
        .results h2 {
            color: #8b4513;
            font-size: 22px;
            margin-top: 0;
            margin-bottom: 20px;
            border-bottom: 2px solid #f7e4d4;
            padding-bottom: 10px;
        }
        
        .order-card {
            background-color: #f9f9f9;
            border-left: 4px solid #8b4513;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 5px;
        }
        
        .order-card p {
            margin: 5px 0;
        }
        
        .order-card .label {
            font-weight: bold;
            color: #8b4513;
            display: inline-block;
            width: 100px;
        }
        
        .no-results {
            text-align: center;
            padding: 20px;
            color: #666;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .spinner {
            border: 4px solid rgba(0, 0, 0, 0.1);
            border-radius: 50%;
            border-top: 4px solid #8b4513;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-message {
            color: #d9534f;
            text-align: center;
            margin-top: 10px;
            display: none;
        }
        
        .back-button {
            display: inline-block;
            background-color: #6d3611;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 24px;
            }
            
            .radio-group {
                flex-direction: column;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>融氏古早味 - 訂單查詢</h1>
            <p>請輸入訂單中電話號碼以查詢訂單</p>
        </div>
        
        <div class="search-form">
            <!-- 移除查詢類型選擇，僅使用電話號碼查詢 -->
            
            <div class="form-group">
                <label for="searchValue">請輸入電話號碼：</label>
                <input type="text" id="searchValue" placeholder="請輸入訂單中電話號碼" required>
                <small style="color: #666; display: block; margin-top: 5px;">提示：僅需輸入數字，系統會自動過濾非數字字符</small>
            </div>
            
            <button type="button" id="searchButton">查詢訂單</button>
            <div class="error-message" id="errorMessage"></div>
        </div>
        
        <div class="loading" id="loading">
            <div class="spinner"></div>
            <p>正在查詢，請稍候...</p>
        </div>
        
        <div class="results" id="results">
            <h2>查詢結果：</h2>
            <div id="ordersList"></div>
            <a href="/pos7.html" class="back-button">返回首頁</a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const searchButton = document.getElementById('searchButton');
            const searchValue = document.getElementById('searchValue');
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const ordersList = document.getElementById('ordersList');
            const errorMessage = document.getElementById('errorMessage');
            
            searchButton.addEventListener('click', function() {
                // 驗證輸入
                if (!searchValue.value.trim()) {
                    errorMessage.textContent = '請輸入查詢內容';
                    errorMessage.style.display = 'block';
                    return;
                }
                
                // 隱藏錯誤訊息和結果，顯示載入中
                errorMessage.style.display = 'none';
                results.style.display = 'none';
                loading.style.display = 'block';
                
                // 發送AJAX請求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'order_query.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                
                xhr.onload = function() {
                    loading.style.display = 'none';

                    if (xhr.status === 200) {
                        // 檢查回應內容是否為空或包含 HTML
                        if (!xhr.responseText || xhr.responseText.trim() === '') {
                            errorMessage.textContent = '伺服器回應為空，請稍後再試';
                            errorMessage.style.display = 'block';
                            console.error('Empty response from server');
                            return;
                        }

                        // 檢查是否包含 HTML 標籤（表示可能是錯誤頁面）
                        if (xhr.responseText.includes('<') && xhr.responseText.includes('>')) {
                            errorMessage.textContent = '伺服器發生錯誤，請稍後再試';
                            errorMessage.style.display = 'block';
                            console.error('Server returned HTML instead of JSON:', xhr.responseText.substring(0, 200));
                            return;
                        }

                        try {
                            const response = JSON.parse(xhr.responseText);

                            if (response.success) {
                                // 顯示結果
                                results.style.display = 'block';
                                
                                // 清空之前的結果
                                ordersList.innerHTML = '';
                                
                                if (response.orders.length > 0) {
                                    // 顯示訂單列表
                                    
                                    // 分類訂單
                                    const recentOrders = [];
                                    const oldOrders = [];
                                    
                                    // 後端已經排序和標記，前端直接使用
                                    // 但前端仍需根據 'timestamp' (訂單時間) 判斷新舊來顯示不同標題
                                    response.orders.forEach(function(order) {
                                        let isRecentOrder = false;
                                        if (order.timestamp) {
                                            // 嘗試解析訂單時間 (格式可能為 m/d/Y H:i:s 或 Y-m-d H:i:s)
                                            let orderTimestampMs;
                                            // 嘗試 ISO 8601 格式 (YYYY-MM-DD HH:MM:SS)
                                            let dateStr = order.timestamp.replace(/\//g, '-');
                                            // 檢查是否包含時間部分
                                            if (dateStr.includes(' ')) {
                                                orderTimestampMs = new Date(dateStr).getTime();
                                            } else {
                                                // 如果只有日期，補上時間 00:00:00
                                                orderTimestampMs = new Date(dateStr + ' 00:00:00').getTime();
                                            }
                                            
                                            // 嘗試 m/d/Y H:i:s 格式
                                            if (isNaN(orderTimestampMs)) {
                                                 const parts = order.timestamp.match(/(\d{1,2})\/(\d{1,2})\/(\d{4})\s*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?/);
                                                 if (parts) {
                                                     const year = parseInt(parts[3], 10);
                                                     const month = parseInt(parts[1], 10) - 1; // JS 月份從 0 開始
                                                     const day = parseInt(parts[2], 10);
                                                     const hour = parseInt(parts[4] || '0', 10);
                                                     const minute = parseInt(parts[5] || '0', 10);
                                                     const second = parseInt(parts[6] || '0', 10);
                                                     orderTimestampMs = new Date(year, month, day, hour, minute, second).getTime();
                                                 }
                                            }

                                            if (!isNaN(orderTimestampMs)) {
                                                const currentTimestamp = new Date().getTime();
                                                const daysDiff = Math.floor((currentTimestamp - orderTimestampMs) / (1000 * 60 * 60 * 24));
                                                if (daysDiff <= 15) {
                                                    isRecentOrder = true;
                                                }
                                            }
                                        }

                                        if (isRecentOrder) {
                                            recentOrders.push(order);
                                        } else {
                                            oldOrders.push(order);
                                        }
                                    });
                                    
                                    // 顯示最新訂單
                                    if (recentOrders.length > 0) {
                                        const recentTitle = document.createElement('h3');
                                        recentTitle.textContent = '最新訂單';
                                        recentTitle.style.color = '#cc0000';
                                        recentTitle.style.borderBottom = '1px solid #f7e4d4';
                                        recentTitle.style.paddingBottom = '5px';
                                        recentTitle.style.marginTop = '15px';
                                        ordersList.appendChild(recentTitle);
                                        
                                        // 顯示最新訂單列表
                                        recentOrders.forEach(function(order) {
                                            const orderCard = document.createElement('div');
                                            orderCard.className = 'order-card';
                                            
                                            orderCard.innerHTML = `
                                                <p><span class="label">訂單時間:</span> ${order.timestamp}</p>
                                                <p><span class="label">姓名:</span> ${order.name}</p>
                                                <p><span class="label">電話:</span> ${order.phone}</p>
                                                <p><span class="label">訂單項目:</span> ${order.orderItems}</p>
                                                <p><span class="label">總金額:</span> ${order.totalAmount}</p>
                                                <p><span class="label">希望到貨日:</span> ${order.deliveryDate}</p>
                                                <p><span class="label">配送方式:</span> ${order.deliveryMethod}</p>
                                            `;
                                            
                                            ordersList.appendChild(orderCard);
                                        });
                                    }
                                    
                                    // 顯示歷史訂單
                                    if (oldOrders.length > 0) {
                                        const oldTitle = document.createElement('h3');
                                        oldTitle.textContent = '歷史訂單';
                                        oldTitle.style.color = '#444444';
                                        oldTitle.style.borderBottom = '1px solid #f7e4d4';
                                        oldTitle.style.paddingBottom = '5px';
                                        oldTitle.style.marginTop = '15px';
                                        ordersList.appendChild(oldTitle);
                                        
                                        // 顯示歷史訂單列表
                                        oldOrders.forEach(function(order) {
                                            const orderCard = document.createElement('div');
                                            orderCard.className = 'order-card';
                                            
                                            orderCard.innerHTML = `
                                                <p><span class="label">訂單時間:</span> ${order.timestamp}</p>
                                                <p><span class="label">姓名:</span> ${order.name}</p>
                                                <p><span class="label">電話:</span> ${order.phone}</p>
                                                <p><span class="label">訂單項目:</span> ${order.orderItems}</p>
                                                <p><span class="label">總金額:</span> ${order.totalAmount}</p>
                                                <p><span class="label">希望到貨日:</span> ${order.deliveryDate}</p>
                                                <p><span class="label">配送方式:</span> ${order.deliveryMethod}</p>
                                            `;
                                            
                                            ordersList.appendChild(orderCard);
                                        });
                                    }
                                }
                                else {
                                    // 沒有找到訂單
                                    ordersList.innerHTML = '<div class="no-results">沒有找到符合條件的訂單</div>';
                                }
                            } else {
                                // 顯示錯誤訊息
                                errorMessage.textContent = response.message;
                                errorMessage.style.display = 'block';
                            }
                        } catch (e) {
                            // JSON解析錯誤
                            errorMessage.textContent = '伺服器回應格式錯誤，請稍後再試';
                            errorMessage.style.display = 'block';
                            console.error('JSON解析錯誤:', e);
                            console.error('伺服器回應內容:', xhr.responseText.substring(0, 500));
                        }
                    } else {
                        // HTTP錯誤
                        errorMessage.textContent = '伺服器錯誤，請稍後再試';
                        errorMessage.style.display = 'block';
                    }
                };
                
                xhr.onerror = function() {
                    loading.style.display = 'none';
                    errorMessage.textContent = '網絡錯誤，請檢查您的連接';
                    errorMessage.style.display = 'block';
                };
                
                // 發送請求，固定使用電話號碼查詢
                xhr.send(`searchValue=${encodeURIComponent(searchValue.value)}`);
            });
            
            // 按Enter鍵也可以提交
            searchValue.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchButton.click();
                }
            });
        });
    </script>
</body>
</html>