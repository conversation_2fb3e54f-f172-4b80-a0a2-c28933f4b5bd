# PowerShell script to fix blank page issues
# Run with: powershell -ExecutionPolicy Bypass -File fix-page.ps1

Write-Host ""
Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   融氏古早味 - 頁面修復工具" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "🔍 檢查系統狀態..." -ForegroundColor Green

# Check if key files exist
$files = @(
    @{Path="components\AlertContextBrowser.js"; Name="AlertContextBrowser.js"},
    @{Path="utils\alertUpgradeUtils.js"; Name="alertUpgradeUtils.js"},
    @{Path="app.js"; Name="app.js"},
    @{Path="index.html"; Name="index.html"}
)

foreach ($file in $files) {
    if (Test-Path $file.Path) {
        Write-Host "✅ $($file.Name) 存在" -ForegroundColor Green
    } else {
        Write-Host "❌ $($file.Name) 不存在" -ForegroundColor Red
    }
}

Write-Host ""
Write-Host "🚀 開啟診斷工具..." -ForegroundColor Yellow

# Open emergency fix tool
Write-Host "🚨 開啟緊急修復工具..." -ForegroundColor Magenta
Start-Process "http://localhost/lopokao/emergency-fix.html"

Start-Sleep -Seconds 2

# Open CDN test
Write-Host "📡 開啟 CDN 測試..." -ForegroundColor Cyan
Start-Process "http://localhost/lopokao/test-cdn.html"

Start-Sleep -Seconds 2

# Open main page
Write-Host "🏠 開啟主頁面..." -ForegroundColor Blue
Start-Process "http://localhost/lopokao/index.html"

Write-Host ""
Write-Host "✅ 所有頁面已開啟！" -ForegroundColor Green
Write-Host ""
Write-Host "💡 如果頁面仍然空白：" -ForegroundColor Yellow
Write-Host "   1. 按 F12 開啟開發者工具檢查錯誤" -ForegroundColor White
Write-Host "   2. 按 Ctrl+F5 清除快取重新載入" -ForegroundColor White
Write-Host "   3. 確認 XAMPP 正在運行" -ForegroundColor White
Write-Host "   4. 使用緊急修復工具進行自動診斷" -ForegroundColor White
Write-Host ""

# Check if XAMPP is running
$xamppProcess = Get-Process -Name "httpd" -ErrorAction SilentlyContinue
if ($xamppProcess) {
    Write-Host "✅ XAMPP Apache 正在運行" -ForegroundColor Green
} else {
    Write-Host "⚠️  XAMPP Apache 可能未運行" -ForegroundColor Yellow
    Write-Host "   請啟動 XAMPP 控制面板並啟動 Apache" -ForegroundColor White
}

Write-Host ""
Write-Host "按任意鍵繼續..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
