/**
 * Alert 系統漸進式升級示例組件
 * 展示如何在同一個專案中同時使用舊版和新版 Alert
 */

function AlertMigrationExample() {
    const [migrationStep, setMigrationStep] = React.useState(1);
    
    // 檢查是否可以使用 useAlert Hook
    const canUseHook = window.AlertUpgradeUtils?.supportsAlertHook();
    
    // 嘗試使用 Hook（如果可用）
    let alertHook = null;
    try {
        if (canUseHook && window.useAlert) {
            alertHook = window.useAlert();
        }
    } catch (error) {
        console.log('Hook not available in this context');
    }

    // 第一階段：使用舊版全域函數
    const demonstrateOldWay = () => {
        console.log('🔄 示範：使用舊版全域函數');
        
        if (window.showSuccess) {
            window.showSuccess(
                '這是使用舊版全域函數的成功訊息\n\n特點：\n• 直接調用 window.showSuccess\n• 向後兼容\n• 簡單易用',
                '舊版 Alert 示範'
            );
        } else {
            alert('舊版 Alert 函數不可用');
        }
    };

    // 第二階段：使用安全包裝器
    const demonstrateSafeWay = () => {
        console.log('🔄 示範：使用安全包裝器');
        
        window.SafeAlert.success(
            '這是使用安全包裝器的成功訊息\n\n特點：\n• 自動檢測可用的 Alert 方法\n• 提供降級方案\n• 更安全可靠',
            '安全包裝器示範'
        );
    };

    // 第三階段：使用新版 Hook（如果可用）
    const demonstrateNewWay = () => {
        console.log('🔄 示範：使用新版 Hook');
        
        if (alertHook) {
            alertHook.showSuccess(
                '這是使用新版 Hook 的成功訊息\n\n特點：\n• React Context 架構\n• 更好的狀態管理\n• 現代化開發體驗',
                '新版 Hook 示範'
            );
        } else {
            // 降級到安全包裝器
            window.SafeAlert.success(
                'Hook 不可用，降級使用安全包裝器\n\n這展示了漸進式升級的優勢：\n• 自動降級\n• 功能不中斷\n• 平滑過渡',
                '降級示範'
            );
        }
    };

    // 混合使用示例
    const demonstrateMixedUsage = () => {
        console.log('🔄 示範：混合使用不同方式');
        
        // 第一個彈窗：使用舊版方式
        window.showInfo('第一個彈窗：舊版方式', '混合示範', () => {
            // 第二個彈窗：使用安全包裝器
            setTimeout(() => {
                window.SafeAlert.warning('第二個彈窗：安全包裝器', '混合示範', () => {
                    // 第三個彈窗：使用新版 Hook（如果可用）
                    setTimeout(() => {
                        if (alertHook) {
                            alertHook.showSuccess('第三個彈窗：新版 Hook', '混合示範');
                        } else {
                            window.SafeAlert.success('第三個彈窗：降級到安全包裝器', '混合示範');
                        }
                    }, 500);
                });
            }, 500);
        });
    };

    // 顯示升級狀態
    const showUpgradeStatus = () => {
        if (window.AlertUpgradeUtils) {
            window.AlertUpgradeUtils.showUpgradeReport();
        } else {
            console.log('升級工具不可用');
        }
    };

    return React.createElement('div', {
        className: 'alert-migration-example p-6 bg-white rounded-lg shadow-lg max-w-4xl mx-auto'
    }, [
        // 標題
        React.createElement('h2', {
            key: 'title',
            className: 'text-2xl font-bold text-gray-800 mb-6 text-center'
        }, 'Alert 系統漸進式升級示範'),

        // 狀態顯示
        React.createElement('div', {
            key: 'status',
            className: 'bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6'
        }, [
            React.createElement('h3', {
                key: 'status-title',
                className: 'text-lg font-semibold text-blue-800 mb-2'
            }, '當前系統狀態'),
            
            React.createElement('div', {
                key: 'status-info',
                className: 'grid grid-cols-1 md:grid-cols-3 gap-4 text-sm'
            }, [
                React.createElement('div', {
                    key: 'version'
                }, [
                    React.createElement('strong', { key: 'v-label' }, '版本: '),
                    window.AlertUpgradeUtils?.getAlertSystemVersion() || '未知'
                ]),
                
                React.createElement('div', {
                    key: 'upgraded'
                }, [
                    React.createElement('strong', { key: 'u-label' }, '已升級: '),
                    window.AlertUpgradeUtils?.isAlertSystemUpgraded() ? '✅ 是' : '❌ 否'
                ]),
                
                React.createElement('div', {
                    key: 'hook'
                }, [
                    React.createElement('strong', { key: 'h-label' }, 'Hook 支援: '),
                    canUseHook ? '✅ 是' : '❌ 否'
                ])
            ])
        ]),

        // 升級階段選擇
        React.createElement('div', {
            key: 'steps',
            className: 'mb-6'
        }, [
            React.createElement('h3', {
                key: 'steps-title',
                className: 'text-lg font-semibold text-gray-800 mb-3'
            }, '升級階段'),
            
            React.createElement('div', {
                key: 'step-buttons',
                className: 'flex flex-wrap gap-2'
            }, [
                React.createElement('button', {
                    key: 'step1',
                    className: `px-4 py-2 rounded-lg transition-colors ${migrationStep === 1 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,
                    onClick: () => setMigrationStep(1)
                }, '階段一：舊版方式'),
                
                React.createElement('button', {
                    key: 'step2',
                    className: `px-4 py-2 rounded-lg transition-colors ${migrationStep === 2 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,
                    onClick: () => setMigrationStep(2)
                }, '階段二：安全包裝器'),
                
                React.createElement('button', {
                    key: 'step3',
                    className: `px-4 py-2 rounded-lg transition-colors ${migrationStep === 3 ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'}`,
                    onClick: () => setMigrationStep(3)
                }, '階段三：新版 Hook')
            ])
        ]),

        // 示範按鈕
        React.createElement('div', {
            key: 'demo-buttons',
            className: 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6'
        }, [
            React.createElement('button', {
                key: 'old-demo',
                className: 'bg-yellow-500 hover:bg-yellow-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors',
                onClick: demonstrateOldWay
            }, '舊版方式示範'),
            
            React.createElement('button', {
                key: 'safe-demo',
                className: 'bg-orange-500 hover:bg-orange-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors',
                onClick: demonstrateSafeWay
            }, '安全包裝器示範'),
            
            React.createElement('button', {
                key: 'new-demo',
                className: 'bg-green-500 hover:bg-green-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors',
                onClick: demonstrateNewWay
            }, '新版 Hook 示範'),
            
            React.createElement('button', {
                key: 'mixed-demo',
                className: 'bg-purple-500 hover:bg-purple-600 text-white font-semibold py-3 px-4 rounded-lg transition-colors',
                onClick: demonstrateMixedUsage
            }, '混合使用示範')
        ]),

        // 工具按鈕
        React.createElement('div', {
            key: 'tools',
            className: 'flex justify-center'
        }, React.createElement('button', {
            className: 'bg-blue-500 hover:bg-blue-600 text-white font-semibold py-2 px-6 rounded-lg transition-colors',
            onClick: showUpgradeStatus
        }, '顯示升級狀態報告'))
    ]);
}

// 全域導出
window.AlertMigrationExample = AlertMigrationExample;
